import { Page, expect } from "@playwright/test";
import { prisma } from "@/lib/prisma";
import { PageData } from "../pages/admin-pages.page";

export interface TestPageData extends PageData {
  id?: string;
  authorId?: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Helper functions for managing test page data
 */

/**
 * Generate test page data with random values
 */
export function generateTestPageData(overrides: Partial<TestPageData> = {}): TestPageData {
  const timestamp = Date.now();
  const randomId = Math.random().toString(36).substring(7);
  
  return {
    title: `Test Page ${randomId}`,
    content: `<p>This is test content for page ${randomId}. Created at ${new Date().toISOString()}.</p><p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>`,
    excerpt: `Test excerpt for page ${randomId}`,
    slug: `test-page-${randomId}-${timestamp}`,
    status: "DRAFT",
    featured: false,
    metaTitle: `Test Page ${randomId} - Meta Title`,
    metaDescription: `Meta description for test page ${randomId}`,
    ...overrides,
  };
}

/**
 * Generate multiple test pages
 */
export function generateTestPages(count: number, baseData: Partial<TestPageData> = {}): TestPageData[] {
  return Array.from({ length: count }, (_, index) => 
    generateTestPageData({
      ...baseData,
      title: `${baseData.title || 'Test Page'} ${index + 1}`,
      slug: `${baseData.slug || 'test-page'}-${index + 1}-${Date.now()}`,
    })
  );
}

/**
 * Create test page in database
 */
export async function createTestPageInDB(pageData: TestPageData, authorId: string): Promise<TestPageData> {
  const page = await prisma.page.create({
    data: {
      title: pageData.title,
      content: pageData.content,
      excerpt: pageData.excerpt || null,
      slug: pageData.slug || `test-page-${Date.now()}`,
      status: pageData.status,
      featured: pageData.featured,
      featuredImage: pageData.featuredImage || null,
      metaTitle: pageData.metaTitle || null,
      metaDescription: pageData.metaDescription || null,
      authorId,
    },
    include: {
      author: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });

  return {
    ...pageData,
    id: page.id,
    authorId: page.authorId,
    createdAt: page.createdAt.toISOString(),
    updatedAt: page.updatedAt.toISOString(),
  };
}

/**
 * Create multiple test pages in database
 */
export async function createTestPagesInDB(
  pagesData: TestPageData[], 
  authorId: string
): Promise<TestPageData[]> {
  const createdPages: TestPageData[] = [];
  
  for (const pageData of pagesData) {
    const createdPage = await createTestPageInDB(pageData, authorId);
    createdPages.push(createdPage);
  }
  
  return createdPages;
}

/**
 * Get test admin user ID
 */
export async function getTestAdminId(): Promise<string> {
  const admin = await prisma.adminUser.findFirst({
    where: {
      email: "<EMAIL>",
      role: "ADMIN",
    },
  });
  
  if (!admin) {
    throw new Error("Test admin user not found. Make sure global setup has run.");
  }
  
  return admin.id;
}

/**
 * Clean up test pages from database
 */
export async function cleanupTestPages(): Promise<void> {
  // Delete pages created by test admin users
  await prisma.page.deleteMany({
    where: {
      OR: [
        { slug: { startsWith: "test-page-" } },
        { title: { startsWith: "Test Page" } },
        { title: { startsWith: "E2E Test" } },
        { author: { email: { in: ["<EMAIL>", "<EMAIL>"] } } },
      ],
    },
  });
}

/**
 * Clean up specific test page by slug
 */
export async function cleanupTestPageBySlug(slug: string): Promise<void> {
  await prisma.page.deleteMany({
    where: { slug },
  });
}

/**
 * Clean up test pages by title pattern
 */
export async function cleanupTestPagesByTitle(titlePattern: string): Promise<void> {
  await prisma.page.deleteMany({
    where: {
      title: { contains: titlePattern },
    },
  });
}

/**
 * Get page from database by slug
 */
export async function getPageBySlug(slug: string): Promise<TestPageData | null> {
  const page = await prisma.page.findUnique({
    where: { slug },
    include: {
      author: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });

  if (!page) return null;

  return {
    id: page.id,
    title: page.title,
    content: page.content,
    excerpt: page.excerpt || undefined,
    slug: page.slug,
    status: page.status as "DRAFT" | "PUBLISHED" | "ARCHIVED",
    featured: page.featured,
    featuredImage: page.featuredImage || undefined,
    metaTitle: page.metaTitle || undefined,
    metaDescription: page.metaDescription || undefined,
    authorId: page.authorId,
    createdAt: page.createdAt.toISOString(),
    updatedAt: page.updatedAt.toISOString(),
  };
}

/**
 * Update page in database
 */
export async function updatePageInDB(id: string, updates: Partial<TestPageData>): Promise<TestPageData> {
  const page = await prisma.page.update({
    where: { id },
    data: {
      ...(updates.title && { title: updates.title }),
      ...(updates.content && { content: updates.content }),
      ...(updates.excerpt !== undefined && { excerpt: updates.excerpt }),
      ...(updates.slug && { slug: updates.slug }),
      ...(updates.status && { status: updates.status }),
      ...(updates.featured !== undefined && { featured: updates.featured }),
      ...(updates.featuredImage !== undefined && { featuredImage: updates.featuredImage }),
      ...(updates.metaTitle !== undefined && { metaTitle: updates.metaTitle }),
      ...(updates.metaDescription !== undefined && { metaDescription: updates.metaDescription }),
    },
    include: {
      author: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });

  return {
    id: page.id,
    title: page.title,
    content: page.content,
    excerpt: page.excerpt || undefined,
    slug: page.slug,
    status: page.status as "DRAFT" | "PUBLISHED" | "ARCHIVED",
    featured: page.featured,
    featuredImage: page.featuredImage || undefined,
    metaTitle: page.metaTitle || undefined,
    metaDescription: page.metaDescription || undefined,
    authorId: page.authorId,
    createdAt: page.createdAt.toISOString(),
    updatedAt: page.updatedAt.toISOString(),
  };
}

/**
 * Count pages in database
 */
export async function countPagesInDB(): Promise<number> {
  return await prisma.page.count();
}

/**
 * Count test pages in database
 */
export async function countTestPagesInDB(): Promise<number> {
  return await prisma.page.count({
    where: {
      OR: [
        { slug: { startsWith: "test-page-" } },
        { title: { startsWith: "Test Page" } },
        { title: { startsWith: "E2E Test" } },
      ],
    },
  });
}

/**
 * Verify page exists in database
 */
export async function verifyPageExistsInDB(slug: string): Promise<boolean> {
  const page = await prisma.page.findUnique({
    where: { slug },
  });
  return !!page;
}

/**
 * Verify page does not exist in database
 */
export async function verifyPageNotExistsInDB(slug: string): Promise<boolean> {
  const page = await prisma.page.findUnique({
    where: { slug },
  });
  return !page;
}

/**
 * Get all test pages from database
 */
export async function getAllTestPagesFromDB(): Promise<TestPageData[]> {
  const pages = await prisma.page.findMany({
    where: {
      OR: [
        { slug: { startsWith: "test-page-" } },
        { title: { startsWith: "Test Page" } },
        { title: { startsWith: "E2E Test" } },
      ],
    },
    include: {
      author: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
    orderBy: { createdAt: "desc" },
  });

  return pages.map(page => ({
    id: page.id,
    title: page.title,
    content: page.content,
    excerpt: page.excerpt || undefined,
    slug: page.slug,
    status: page.status as "DRAFT" | "PUBLISHED" | "ARCHIVED",
    featured: page.featured,
    featuredImage: page.featuredImage || undefined,
    metaTitle: page.metaTitle || undefined,
    metaDescription: page.metaDescription || undefined,
    authorId: page.authorId,
    createdAt: page.createdAt.toISOString(),
    updatedAt: page.updatedAt.toISOString(),
  }));
}
