import { Page, expect } from "@playwright/test";
import { prisma } from "@/lib/prisma";
import bcrypt from "bcryptjs";

export interface TestPost {
  title: string;
  content: string;
  excerpt?: string;
  slug?: string;
  status: "DRAFT" | "PUBLISHED" | "ARCHIVED";
  featured: boolean;
  featuredImage?: string;
  tags: string[];
  categoryId?: string;
}

export interface TestCategory {
  name: string;
  slug: string;
  description?: string;
}

export interface TestAdminUser {
  email: string;
  password: string;
  name: string;
  role: "ADMIN" | "MODERATOR";
}

// Test data
export const TEST_POSTS: TestPost[] = [
  {
    title: "Hướng dẫn chọn size áo thun phù hợp",
    content:
      "<p>Việc chọn size áo thun phù hợp là rất quan trọng để đảm bảo sự thoải mái và phong cách. Dưới đây là những hướng dẫn chi tiết...</p>",
    excerpt: "Cách chọn size áo thun phù hợp với từng dáng người",
    slug: "huong-dan-chon-size-ao-thun-phu-hop",
    status: "PUBLISHED",
    featured: true,
    tags: ["hướng dẫn", "thời trang", "áo thun"],
  },
  {
    title: "Xu hướng thời trang mùa hè 2024",
    content:
      "<p>Mùa hè 2024 đánh dấu sự trở lại của nhiều phong cách thời trang độc đáo. Từ màu sắc tươi sáng đến những thiết kế tối giản...</p>",
    excerpt: "Tổng hợp các xu hướng thời trang hot nhất mùa hè 2024",
    slug: "xu-huong-thoi-trang-mua-he-2024",
    status: "PUBLISHED",
    featured: false,
    tags: ["xu hướng", "thời trang", "mùa hè"],
  },
  {
    title: "Bảo quản giày sneaker đúng cách",
    content:
      "<p>Giày sneaker là một phần không thể thiếu trong tủ đồ của nhiều người. Để giữ cho đôi giày luôn như mới...</p>",
    excerpt: "Mẹo bảo quản giày sneaker để giữ form và màu sắc",
    slug: "bao-quan-giay-sneaker-dung-cach",
    status: "DRAFT",
    featured: false,
    tags: ["bảo quản", "giày", "sneaker"],
  },
  {
    title: "Phối đồ công sở thanh lịch cho nữ",
    content:
      "<p>Phong cách công sở thanh lịch không chỉ thể hiện sự chuyên nghiệp mà còn giúp bạn tự tin hơn trong công việc...</p>",
    excerpt: "Cách phối đồ công sở thanh lịch và chuyên nghiệp",
    slug: "phoi-do-cong-so-thanh-lich-cho-nu",
    status: "ARCHIVED",
    featured: false,
    tags: ["công sở", "phối đồ", "thanh lịch"],
  },
];

export const TEST_CATEGORIES: TestCategory[] = [
  {
    name: "Hướng dẫn",
    slug: "huong-dan",
    description: "Các bài viết hướng dẫn về thời trang",
  },
  {
    name: "Xu hướng",
    slug: "xu-huong",
    description: "Xu hướng thời trang mới nhất",
  },
  {
    name: "Bảo quản",
    slug: "bao-quan",
    description: "Hướng dẫn bảo quản quần áo và phụ kiện",
  },
];

export const TEST_ADMIN_USERS: TestAdminUser[] = [
  {
    email: "<EMAIL>",
    password: "admin123",
    name: "Test Admin",
    role: "ADMIN",
  },
  {
    email: "<EMAIL>",
    password: "moderator123",
    name: "Test Moderator",
    role: "MODERATOR",
  },
];

/**
 * Helper class for Posts page interactions
 */
export class PostsPageHelper {
  constructor(private page: Page) {}

  // Navigation
  async navigateToPostsList() {
    await this.page.goto("/admin/posts");
    await this.page.waitForLoadState("networkidle");
  }

  async navigateToCreatePost() {
    await this.page.goto("/admin/posts/create");
    await this.page.waitForLoadState("networkidle");
  }

  async navigateToEditPost(postId: string) {
    await this.page.goto(`/admin/posts/${postId}/edit`);
    await this.page.waitForLoadState("networkidle");
  }

  // List page interactions
  async searchPosts(query: string) {
    await this.page.fill('[data-testid="search-input"]', query);
    await this.page.press('[data-testid="search-input"]', "Enter");
    await this.page.waitForLoadState("networkidle");
  }

  async filterByStatus(status: string) {
    await this.page.click('[data-testid="status-filter"]');
    await this.page.click(`text=${status}`);
    await this.page.waitForLoadState("networkidle");
  }

  async filterByFeatured(featured: boolean) {
    const checkbox = this.page.locator('[data-testid="featured-filter"]');
    if (featured) {
      await checkbox.check();
    } else {
      await checkbox.uncheck();
    }
    await this.page.waitForLoadState("networkidle");
  }

  async sortBy(field: string, order: "asc" | "desc" = "asc") {
    const sortButton = this.page.locator(`[data-testid="sort-${field}"]`);
    await sortButton.click();
    if (order === "desc") {
      await sortButton.click(); // Click again for descending
    }
    await this.page.waitForLoadState("networkidle");
  }

  async selectPost(index: number) {
    await this.page.check(
      `[data-testid="post-row"]:nth-child(${index + 1}) [data-testid="select-checkbox"]`
    );
  }

  async selectAllPosts() {
    await this.page.check('[data-testid="select-all-checkbox"]');
  }

  async bulkDelete() {
    await this.page.click('[data-testid="bulk-delete-btn"]');
    await this.page.click('[data-testid="confirm-bulk-delete-btn"]');
    await this.page.waitForLoadState("networkidle");
  }

  async deletePost(index: number) {
    await this.page.click(
      `[data-testid="post-row"]:nth-child(${index + 1}) [data-testid="delete-btn"]`
    );
    await this.page.click('[data-testid="confirm-delete-btn"]');
    await this.page.waitForLoadState("networkidle");
  }

  async toggleFeatured(index: number) {
    await this.page.click(
      `[data-testid="post-row"]:nth-child(${index + 1}) [data-testid="featured-btn"]`
    );
    await this.page.waitForLoadState("networkidle");
  }

  // Form interactions
  async fillPostForm(post: Partial<TestPost>) {
    if (post.title) {
      await this.page.fill('[data-testid="title-input"]', post.title);
    }

    if (post.content) {
      // Wait for Quill editor to load
      await this.page.waitForSelector(".ql-editor");
      await this.page.locator(".ql-editor").fill(post.content);
    }

    if (post.excerpt) {
      await this.page.fill('[data-testid="excerpt-input"]', post.excerpt);
    }

    if (post.slug) {
      await this.page.fill('[data-testid="slug-input"]', post.slug);
    }

    if (post.status) {
      await this.page.click('[data-testid="status-select"]');
      await this.page.click(`text=${post.status}`);
    }

    if (post.featured !== undefined) {
      const featuredSwitch = this.page.locator(
        '[data-testid="featured-switch"]'
      );
      if (post.featured) {
        await featuredSwitch.check();
      } else {
        await featuredSwitch.uncheck();
      }
    }

    if (post.categoryId) {
      await this.page.click('[data-testid="category-select"]');
      await this.page.click(`[data-value="${post.categoryId}"]`);
    }

    if (post.tags && post.tags.length > 0) {
      for (const tag of post.tags) {
        await this.page.fill('[data-testid="tag-input"]', tag);
        await this.page.press('[data-testid="tag-input"]', "Enter");
      }
    }
  }

  async submitForm() {
    await this.page.click('[data-testid="submit-btn"]');
    await this.page.waitForLoadState("networkidle");
  }

  async saveAsDraft() {
    await this.page.click('[data-testid="save-draft-btn"]');
    await this.page.waitForLoadState("networkidle");
  }

  async publishPost() {
    await this.page.click('[data-testid="publish-btn"]');
    await this.page.waitForLoadState("networkidle");
  }

  // Assertions
  async expectPostsCount(count: number) {
    await expect(this.page.locator('[data-testid="post-row"]')).toHaveCount(
      count
    );
  }

  async expectPostVisible(title: string) {
    await expect(this.page.locator(`text=${title}`)).toBeVisible();
  }

  async expectPostNotVisible(title: string) {
    await expect(this.page.locator(`text=${title}`)).not.toBeVisible();
  }

  async expectSuccessMessage(message: string) {
    await expect(this.page.locator(`text=${message}`)).toBeVisible();
  }

  async expectErrorMessage(message: string) {
    await expect(this.page.locator(`text=${message}`)).toBeVisible();
  }

  async expectFormError(field: string, message: string) {
    await expect(
      this.page.locator(`[data-testid="${field}-error"]`)
    ).toContainText(message);
  }

  async expectPageTitle(title: string) {
    await expect(this.page.locator("h1")).toContainText(title);
  }

  async expectUrl(url: string | RegExp) {
    await expect(this.page).toHaveURL(url);
  }
}

/**
 * Database helper functions
 */
export class PostsDbHelper {
  static async cleanupTestData() {
    await prisma.post.deleteMany({
      where: {
        OR: [
          { title: { contains: "Test" } },
          { slug: { startsWith: "test-" } },
          { author: { email: { endsWith: "@test.com" } } },
        ],
      },
    });

    await prisma.category.deleteMany({
      where: {
        slug: { in: TEST_CATEGORIES.map((c) => c.slug) },
      },
    });

    await prisma.adminUser.deleteMany({
      where: {
        email: { in: TEST_ADMIN_USERS.map((u) => u.email) },
      },
    });
  }

  static async createTestCategories() {
    const categories = [];
    for (const category of TEST_CATEGORIES) {
      try {
        // Try to find existing category first
        const existing = await prisma.category.findUnique({
          where: { slug: category.slug },
        });

        if (existing) {
          categories.push(existing);
          continue;
        }

        // Create new category if not exists
        const created = await prisma.category.create({
          data: category,
        });
        categories.push(created);
      } catch (error: any) {
        // Handle race condition - try to find again
        if (error.code === "P2002") {
          const existing = await prisma.category.findUnique({
            where: { slug: category.slug },
          });

          if (existing) {
            categories.push(existing);
            continue;
          }
        }

        throw error;
      }
    }
    return categories;
  }

  static async createTestAdminUser(userData: TestAdminUser) {
    try {
      // Try to find existing user first
      const existingUser = await prisma.adminUser.findUnique({
        where: { email: userData.email },
      });

      if (existingUser) {
        return existingUser;
      }

      // Create new user if not exists
      return await prisma.adminUser.create({
        data: {
          ...userData,
          password: await bcrypt.hash(userData.password, 10),
        },
      });
    } catch (error: any) {
      // If creation fails due to unique constraint, try to find again
      // This handles race conditions in parallel tests
      if (error.code === "P2002") {
        const existingUser = await prisma.adminUser.findUnique({
          where: { email: userData.email },
        });

        if (existingUser) {
          return existingUser;
        }
      }

      throw error;
    }
  }

  static async createTestPosts(authorId: string, categoryId?: string) {
    const posts = [];

    // Ensure we have a valid categoryId
    let validCategoryId = categoryId;
    if (!validCategoryId) {
      // Create a default category if none provided
      const defaultCategory = await prisma.category.upsert({
        where: { slug: "test-category" },
        update: {},
        create: {
          name: "Test Category",
          slug: "test-category",
          description: "Default test category",
        },
      });
      validCategoryId = defaultCategory.id;
    }

    for (const postData of TEST_POSTS) {
      // Generate slug outside try-catch so it's available in catch block
      const slug =
        postData.slug ||
        postData.title
          .toLowerCase()
          .replace(/\s+/g, "-")
          .replace(/[^a-z0-9-]/g, "");

      try {
        // Try to find existing post first
        const existing = await prisma.post.findUnique({
          where: { slug },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            category: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
        });

        if (existing) {
          posts.push(existing);
          continue;
        }

        // Create new post if not exists
        const created = await prisma.post.create({
          data: {
            ...postData,
            slug,
            authorId,
            categoryId: validCategoryId,
          },
          include: {
            author: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            category: {
              select: {
                id: true,
                name: true,
                slug: true,
              },
            },
          },
        });
        posts.push(created);
      } catch (error: any) {
        // Handle race condition - try to find again
        if (error.code === "P2002") {
          const existing = await prisma.post.findUnique({
            where: { slug },
            include: {
              author: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
              category: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                },
              },
            },
          });

          if (existing) {
            posts.push(existing);
            continue;
          }
        }

        throw error;
      }
    }
    return posts;
  }

  static async getPostBySlug(slug: string) {
    return await prisma.post.findUnique({
      where: { slug },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
    });
  }

  static async getPostsCount(filters?: {
    status?: string;
    featured?: boolean;
    authorId?: string;
  }) {
    const where: any = {};

    if (filters?.status) {
      where.status = filters.status;
    }

    if (filters?.featured !== undefined) {
      where.featured = filters.featured;
    }

    if (filters?.authorId) {
      where.authorId = filters.authorId;
    }

    return await prisma.post.count({ where });
  }
}
