import { test, expect } from "@playwright/test";
import {
  adminLogin,
  createTestMenu,
  cleanupTestData,
  waitForToast,
  waitForDialog,
  fillMenuForm,
  verifyMenuInList,
  clickMenuAction,
  verifyMenuDetailDialog,
  closeDialog,
} from "./helpers/admin-test-helpers";

test.describe("Admin Menu Management", () => {
  test.beforeEach(async ({ page }) => {
    // Đăng nhập admin trước mỗi test
    await adminLogin(page);

    // Điều hướng đến trang quản lý menu
    await page.goto("/admin/menus");
    await page.waitForLoadState("networkidle");
  });

  test.afterEach(async ({ page }) => {
    // Dọn dẹp dữ liệu test
    await cleanupTestData(page, "menus");
  });

  test("Hiển thị danh sách menu và các thành phần cơ bản", async ({ page }) => {
    // Kiểm tra tiêu đề trang
    await expect(page.locator("h1")).toContainText("Quản lý Menu");

    // Kiểm tra nút tạo menu mới
    await expect(page.locator('button:has-text("Tạo Menu Mới")')).toBeVisible();

    // Kiểm tra bảng danh sách menu
    await expect(
      page.locator('[data-testid="admin-data-table"]')
    ).toBeVisible();

    // Kiểm tra các cột trong bảng
    await expect(page.locator('th:has-text("Tên Menu")')).toBeVisible();
    await expect(page.locator('th:has-text("Vị trí")')).toBeVisible();
    await expect(page.locator('th:has-text("Trạng thái")')).toBeVisible();
    await expect(page.locator('th:has-text("Ngày tạo")')).toBeVisible();
  });

  test("Tạo menu mới thành công", async ({ page }) => {
    // Click nút tạo menu mới
    await page.click('button:has-text("Tạo Menu Mới")');

    // Kiểm tra dialog mở
    await expect(
      page.locator('[role="dialog"]:has-text("Tạo Menu Mới")')
    ).toBeVisible();

    // Điền thông tin menu
    await page.fill("#name", "Test Menu Header");
    await page.selectOption('[data-testid="location-select"]', "header");
    await page.fill("#description", "Menu test cho header navigation");

    // Submit form
    await page.click('button:has-text("Tạo Menu")');

    // Kiểm tra thông báo thành công
    await expect(
      page.locator('.sonner-toast:has-text("Tạo menu thành công")')
    ).toBeVisible();

    // Kiểm tra menu mới xuất hiện trong danh sách
    await expect(page.locator('td:has-text("Test Menu Header")')).toBeVisible();
  });

  test("Xem chi tiết menu", async ({ page }) => {
    // Tạo menu test trước
    const testMenu = await createTestMenu(page, {
      name: "Test Menu Detail",
      location: "header",
      description: "Menu để test xem chi tiết",
    });

    // Refresh trang để thấy menu mới
    await page.reload();
    await page.waitForLoadState("networkidle");

    // Tìm và click nút "Xem chi tiết" của menu test
    const menuRow = page.locator('tr:has-text("Test Menu Detail")');
    await menuRow.locator('button[data-testid="actions-menu"]').click();
    await page.click('button:has-text("Xem chi tiết")');

    // Kiểm tra dialog chi tiết mở
    await expect(
      page.locator(
        '[role="dialog"]:has-text("Chi tiết Menu: Test Menu Detail")'
      )
    ).toBeVisible();

    // Kiểm tra thông tin menu hiển thị đúng
    await expect(page.locator("text=Test Menu Detail")).toBeVisible();
    await expect(page.locator("text=Header Navigation")).toBeVisible();
    await expect(page.locator("text=Menu để test xem chi tiết")).toBeVisible();
    await expect(page.locator("text=Hoạt động")).toBeVisible();

    // Kiểm tra phần cấu trúc menu
    await expect(page.locator("text=Cấu trúc Menu")).toBeVisible();
    await expect(
      page.locator('button:has-text("Mở rộng tất cả")')
    ).toBeVisible();

    // Đóng dialog
    await page.click('button:has-text("Đóng")');
    await expect(
      page.locator('[role="dialog"]:has-text("Chi tiết Menu")')
    ).not.toBeVisible();
  });

  test("Chỉnh sửa menu thành công", async ({ page }) => {
    // Tạo menu test trước
    const testMenu = await createTestMenu(page, {
      name: "Test Menu Edit",
      location: "footer",
      description: "Menu để test chỉnh sửa",
    });

    // Refresh trang để thấy menu mới
    await page.reload();
    await page.waitForLoadState("networkidle");

    // Tìm và click nút "Chỉnh sửa" của menu test
    const menuRow = page.locator('tr:has-text("Test Menu Edit")');
    await menuRow.locator('button[data-testid="actions-menu"]').click();
    await page.click('button:has-text("Chỉnh sửa")');

    // Kiểm tra dialog chỉnh sửa mở
    await expect(
      page.locator('[role="dialog"]:has-text("Chỉnh sửa Menu")')
    ).toBeVisible();

    // Kiểm tra form đã được điền sẵn thông tin
    await expect(page.locator("#edit-name")).toHaveValue("Test Menu Edit");
    await expect(page.locator("#edit-description")).toHaveValue(
      "Menu để test chỉnh sửa"
    );

    // Chỉnh sửa thông tin
    await page.fill("#edit-name", "Test Menu Edit Updated");
    await page.selectOption('[data-testid="edit-location-select"]', "sidebar");
    await page.fill("#edit-description", "Menu đã được cập nhật");

    // Submit form
    await page.click('button:has-text("Cập nhật Menu")');

    // Kiểm tra thông báo thành công
    await expect(
      page.locator('.sonner-toast:has-text("Cập nhật menu thành công")')
    ).toBeVisible();

    // Kiểm tra thông tin đã được cập nhật trong danh sách
    await expect(
      page.locator('td:has-text("Test Menu Edit Updated")')
    ).toBeVisible();
    await expect(
      page.locator('td:has-text("Sidebar Navigation")')
    ).toBeVisible();
  });

  test("Validation form tạo menu", async ({ page }) => {
    // Click nút tạo menu mới
    await page.click('button:has-text("Tạo Menu Mới")');

    // Submit form trống
    await page.click('button:has-text("Tạo Menu")');

    // Kiểm tra thông báo lỗi
    await expect(
      page.locator(
        '.sonner-toast:has-text("Vui lòng điền đầy đủ thông tin bắt buộc")'
      )
    ).toBeVisible();

    // Điền chỉ tên menu
    await page.fill("#name", "Test Menu Validation");
    await page.click('button:has-text("Tạo Menu")');

    // Vẫn phải có lỗi vì chưa chọn vị trí
    await expect(
      page.locator(
        '.sonner-toast:has-text("Vui lòng điền đầy đủ thông tin bắt buộc")'
      )
    ).toBeVisible();
  });

  test("Validation form chỉnh sửa menu", async ({ page }) => {
    // Tạo menu test trước
    const testMenu = await createTestMenu(page, {
      name: "Test Menu Validation Edit",
      location: "header",
      description: "Menu để test validation",
    });

    // Refresh trang để thấy menu mới
    await page.reload();
    await page.waitForLoadState("networkidle");

    // Mở dialog chỉnh sửa
    const menuRow = page.locator('tr:has-text("Test Menu Validation Edit")');
    await menuRow.locator('button[data-testid="actions-menu"]').click();
    await page.click('button:has-text("Chỉnh sửa")');

    // Xóa tên menu
    await page.fill("#edit-name", "");
    await page.click('button:has-text("Cập nhật Menu")');

    // Kiểm tra thông báo lỗi
    await expect(
      page.locator(
        '.sonner-toast:has-text("Vui lòng điền đầy đủ thông tin bắt buộc")'
      )
    ).toBeVisible();
  });

  test("Tìm kiếm menu", async ({ page }) => {
    // Tạo một số menu test
    await createTestMenu(page, {
      name: "Header Menu Test",
      location: "header",
    });
    await createTestMenu(page, {
      name: "Footer Menu Test",
      location: "footer",
    });
    await createTestMenu(page, {
      name: "Sidebar Menu Test",
      location: "sidebar",
    });

    // Refresh trang
    await page.reload();
    await page.waitForLoadState("networkidle");

    // Tìm kiếm menu
    await page.fill('[data-testid="search-input"]', "Header");
    await page.waitForTimeout(500); // Đợi debounce

    // Kiểm tra kết quả tìm kiếm
    await expect(page.locator('td:has-text("Header Menu Test")')).toBeVisible();
    await expect(
      page.locator('td:has-text("Footer Menu Test")')
    ).not.toBeVisible();
    await expect(
      page.locator('td:has-text("Sidebar Menu Test")')
    ).not.toBeVisible();

    // Xóa tìm kiếm
    await page.fill('[data-testid="search-input"]', "");
    await page.waitForTimeout(500);

    // Kiểm tra tất cả menu hiển thị lại
    await expect(page.locator('td:has-text("Header Menu Test")')).toBeVisible();
    await expect(page.locator('td:has-text("Footer Menu Test")')).toBeVisible();
    await expect(
      page.locator('td:has-text("Sidebar Menu Test")')
    ).toBeVisible();
  });

  test("Xóa menu", async ({ page }) => {
    // Tạo menu test
    const testMenu = await createTestMenu(page, {
      name: "Test Menu Delete",
      location: "header",
      description: "Menu để test xóa",
    });

    // Refresh trang
    await page.reload();
    await page.waitForLoadState("networkidle");

    // Mở menu actions và click xóa
    const menuRow = page.locator('tr:has-text("Test Menu Delete")');
    await menuRow.locator('button[data-testid="actions-menu"]').click();
    await page.click('button:has-text("Xóa")');

    // Xác nhận xóa (nếu có dialog xác nhận)
    if (
      await page.locator('[role="dialog"]:has-text("Xác nhận xóa")').isVisible()
    ) {
      await page.click('button:has-text("Xóa")');
    }

    // Kiểm tra menu đã bị xóa khỏi danh sách
    await expect(
      page.locator('td:has-text("Test Menu Delete")')
    ).not.toBeVisible();
  });

  test("Responsive design trên mobile", async ({ page }) => {
    // Thiết lập viewport mobile
    await page.setViewportSize({ width: 375, height: 667 });

    // Kiểm tra trang vẫn hiển thị đúng
    await expect(page.locator('h1:has-text("Quản lý Menu")')).toBeVisible();
    await expect(page.locator('button:has-text("Tạo Menu Mới")')).toBeVisible();

    // Kiểm tra bảng responsive
    await expect(
      page.locator('[data-testid="admin-data-table"]')
    ).toBeVisible();
  });
});
