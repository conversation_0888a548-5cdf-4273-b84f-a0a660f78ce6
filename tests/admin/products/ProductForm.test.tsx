/**
 * Test cho ProductForm Component
 *
 * Bài test này kiểm tra các chức năng ch<PERSON>h của ProductForm component:
 * - Validation logic
 * - Form submission
 * - Error handling
 * - Preset integration
 */

import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { ProductForm } from "@/components/admin/products/ProductForm";
import { toast } from "sonner";

// Mock dependencies
jest.mock("sonner", () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

jest.mock("next/navigation", () => ({
  useRouter: () => ({
    push: jest.fn(),
    back: jest.fn(),
  }),
}));

// Mock fetch
global.fetch = jest.fn();

describe("ProductForm Component", () => {
  const mockOnSubmit = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ categories: [], brands: [] }),
    });
  });

  describe("Validation Tests", () => {
    it("should show validation errors for empty required fields", async () => {
      const user = userEvent.setup();

      render(
        <ProductForm mode="create" onSubmit={mockOnSubmit} loading={false} />
      );

      // Try to submit empty form
      const submitButton = screen.getByRole("button", {
        name: /tạo sản phẩm/i,
      });
      await user.click(submitButton);

      // Check for validation errors
      expect(screen.getByText("Tên sản phẩm là bắt buộc")).toBeInTheDocument();
      expect(
        screen.getByText("Mô tả sản phẩm là bắt buộc")
      ).toBeInTheDocument();
      expect(screen.getByText("Vui lòng chọn danh mục")).toBeInTheDocument();
      expect(
        screen.getByText("Giá sản phẩm phải lớn hơn 0")
      ).toBeInTheDocument();
    });

    it("should validate name length constraints", async () => {
      const user = userEvent.setup();

      render(
        <ProductForm mode="create" onSubmit={mockOnSubmit} loading={false} />
      );

      const nameInput = screen.getByLabelText(/tên sản phẩm/i);

      // Test minimum length
      await user.type(nameInput, "ab");
      fireEvent.blur(nameInput);

      expect(
        screen.getByText("Tên sản phẩm phải có ít nhất 3 ký tự")
      ).toBeInTheDocument();

      // Test maximum length
      const longName = "a".repeat(256);
      await user.clear(nameInput);
      await user.type(nameInput, longName);
      fireEvent.blur(nameInput);

      expect(
        screen.getByText("Tên sản phẩm không được vượt quá 255 ký tự")
      ).toBeInTheDocument();
    });

    it("should validate price constraints", async () => {
      const user = userEvent.setup();

      render(
        <ProductForm mode="create" onSubmit={mockOnSubmit} loading={false} />
      );

      const priceInput = screen.getByLabelText(/giá gốc/i);
      const salePriceInput = screen.getByLabelText(/giá khuyến mãi/i);

      // Test negative price
      await user.type(priceInput, "-100");
      fireEvent.blur(priceInput);

      expect(
        screen.getByText("Giá sản phẩm phải lớn hơn 0")
      ).toBeInTheDocument();

      // Test sale price higher than regular price
      await user.clear(priceInput);
      await user.type(priceInput, "100000");
      await user.type(salePriceInput, "150000");
      fireEvent.blur(salePriceInput);

      expect(
        screen.getByText("Giá khuyến mãi phải nhỏ hơn giá gốc")
      ).toBeInTheDocument();
    });
  });

  describe("Form Submission Tests", () => {
    it("should submit valid form data", async () => {
      const user = userEvent.setup();

      render(
        <ProductForm mode="create" onSubmit={mockOnSubmit} loading={false} />
      );

      // Fill in valid form data
      await user.type(screen.getByLabelText(/tên sản phẩm/i), "Test Product");
      await user.type(
        screen.getByLabelText(/mô tả sản phẩm/i),
        "This is a test product description"
      );
      await user.type(screen.getByLabelText(/giá gốc/i), "100000");

      // Mock category selection
      const categorySelect = screen.getByRole("combobox", {
        name: /danh mục/i,
      });
      await user.click(categorySelect);
      // Note: In real test, you'd mock the categories data and select an option

      // Submit form
      const submitButton = screen.getByRole("button", {
        name: /tạo sản phẩm/i,
      });
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalledWith(
          expect.objectContaining({
            name: "Test Product",
            description: "This is a test product description",
            price: 100000,
          })
        );
      });
    });

    it("should handle submission errors", async () => {
      const user = userEvent.setup();
      mockOnSubmit.mockRejectedValue(new Error("Submission failed"));

      render(
        <ProductForm mode="create" onSubmit={mockOnSubmit} loading={false} />
      );

      // Fill in minimal valid data and submit
      await user.type(screen.getByLabelText(/tên sản phẩm/i), "Test Product");
      await user.type(
        screen.getByLabelText(/mô tả sản phẩm/i),
        "Test description"
      );
      await user.type(screen.getByLabelText(/giá gốc/i), "100000");

      const submitButton = screen.getByRole("button", {
        name: /tạo sản phẩm/i,
      });
      await user.click(submitButton);

      // Should not crash and handle error gracefully
      await waitFor(() => {
        expect(mockOnSubmit).toHaveBeenCalled();
      });
    });
  });

  describe("Image Management Tests", () => {
    it("should require at least one image", async () => {
      const user = userEvent.setup();

      render(
        <ProductForm mode="create" onSubmit={mockOnSubmit} loading={false} />
      );

      // Fill in other required fields but no images
      await user.type(screen.getByLabelText(/tên sản phẩm/i), "Test Product");
      await user.type(
        screen.getByLabelText(/mô tả sản phẩm/i),
        "Test description"
      );
      await user.type(screen.getByLabelText(/giá gốc/i), "100000");

      const submitButton = screen.getByRole("button", {
        name: /tạo sản phẩm/i,
      });
      await user.click(submitButton);

      expect(
        screen.getByText("Vui lòng thêm ít nhất một hình ảnh")
      ).toBeInTheDocument();
    });
  });

  describe("Tags Management Tests", () => {
    it("should add and remove tags", async () => {
      const user = userEvent.setup();

      render(
        <ProductForm mode="create" onSubmit={mockOnSubmit} loading={false} />
      );

      const tagInput = screen.getByLabelText(/thêm tag/i);
      const addButton = screen.getByRole("button", { name: /\+/ });

      // Add a tag
      await user.type(tagInput, "fashion");
      await user.click(addButton);

      expect(screen.getByText("fashion")).toBeInTheDocument();

      // Remove the tag
      const removeButton = screen.getByRole("button", { name: /×/ });
      await user.click(removeButton);

      expect(screen.queryByText("fashion")).not.toBeInTheDocument();
    });

    it("should prevent duplicate tags", async () => {
      const user = userEvent.setup();

      render(
        <ProductForm
          mode="create"
          onSubmit={mockOnSubmit}
          loading={false}
          initialData={{ tags: ["existing"] }}
        />
      );

      const tagInput = screen.getByLabelText(/thêm tag/i);
      const addButton = screen.getByRole("button", { name: /\+/ });

      // Try to add duplicate tag
      await user.type(tagInput, "existing");
      await user.click(addButton);

      // Should only have one instance
      const tags = screen.getAllByText("existing");
      expect(tags).toHaveLength(1);
    });
  });

  describe("Loading States", () => {
    it("should show loading state during submission", () => {
      render(
        <ProductForm mode="create" onSubmit={mockOnSubmit} loading={true} />
      );

      expect(screen.getByText("Đang lưu...")).toBeInTheDocument();
      expect(screen.getByRole("button", { name: /đang lưu/i })).toBeDisabled();
    });
  });

  describe("Edit Mode Tests", () => {
    it("should populate form with initial data in edit mode", () => {
      const initialData = {
        name: "Existing Product",
        description: "Existing description",
        price: 50000,
        salePrice: 40000,
        sku: "PROD-001",
        stock: 10,
        featured: true,
        tags: ["tag1", "tag2"],
      };

      render(
        <ProductForm
          mode="edit"
          productId="123"
          onSubmit={mockOnSubmit}
          loading={false}
          initialData={initialData}
        />
      );

      expect(screen.getByDisplayValue("Existing Product")).toBeInTheDocument();
      expect(
        screen.getByDisplayValue("Existing description")
      ).toBeInTheDocument();
      expect(screen.getByDisplayValue("50000")).toBeInTheDocument();
      expect(screen.getByDisplayValue("40000")).toBeInTheDocument();
      expect(screen.getByDisplayValue("PROD-001")).toBeInTheDocument();
      expect(screen.getByDisplayValue("10")).toBeInTheDocument();
      expect(screen.getByText("tag1")).toBeInTheDocument();
      expect(screen.getByText("tag2")).toBeInTheDocument();
    });
  });
});
