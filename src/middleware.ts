import { NextRequest, NextResponse } from "next/server";
import { jwtVerify } from "jose";
import { withAuth } from "next-auth/middleware";

const secret = new TextEncoder().encode(
  process.env.NEXTAUTH_SECRET || "fallback-secret"
);

async function verifyAdminToken(request: NextRequest) {
  try {
    const adminSession = request.cookies.get("admin-session")?.value;
    if (!adminSession) return null;

    const { payload } = await jwtVerify(adminSession, secret);
    return payload;
  } catch {
    return null;
  }
}

async function handleAdminRoutes(req: NextRequest) {
  const { pathname } = req.nextUrl;

  // Allow admin auth routes and admin login API without authentication
  if (
    pathname.startsWith("/admin/auth") ||
    pathname.startsWith("/api/admin-login") ||
    pathname.startsWith("/api/admin-logout")
  ) {
    return NextResponse.next();
  }

  // Check admin session from cookie
  const adminToken = await verifyAdminToken(req);

  if (!adminToken) {
    return NextResponse.redirect(new URL("/admin/auth/signin", req.url));
  }

  // Check if user is admin type with appropriate role
  if (
    adminToken.type !== "admin" ||
    (adminToken.role !== "ADMIN" && adminToken.role !== "MODERATOR")
  ) {
    return NextResponse.redirect(new URL("/admin/auth/signin", req.url));
  }

  // Additional permission checks for moderators
  if (adminToken.role === "MODERATOR") {
    // Restrict certain admin routes for moderators
    const restrictedPaths = ["/admin/admins", "/admin/settings"];
    if (restrictedPaths.some((path) => pathname.startsWith(path))) {
      return NextResponse.redirect(new URL("/admin", req.url));
    }
  }

  return NextResponse.next();
}

// User auth middleware using NextAuth
const userAuthMiddleware = withAuth(
  function middleware(req) {
    const { pathname } = req.nextUrl;
    const token = req.nextauth.token;

    // User protected routes
    if (
      pathname.startsWith("/profile") ||
      pathname.startsWith("/orders") ||
      pathname.startsWith("/checkout")
    ) {
      if (!token) {
        return NextResponse.redirect(new URL("/auth/signin", req.url));
      }
    }

    // Redirect authenticated users away from auth pages
    if (
      token &&
      (pathname.startsWith("/auth/signin") ||
        pathname.startsWith("/auth/signup"))
    ) {
      return NextResponse.redirect(new URL("/", req.url));
    }

    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl;

        // Allow access to public routes
        if (
          pathname.startsWith("/auth") ||
          pathname.startsWith("/api/auth") ||
          pathname.startsWith("/api/admin-login") ||
          pathname.startsWith("/api/products") ||
          pathname.startsWith("/api/categories") ||
          pathname.startsWith("/api/settings") ||
          pathname === "/" ||
          pathname.startsWith("/products") ||
          pathname.startsWith("/categories") ||
          pathname.startsWith("/_next") ||
          pathname.startsWith("/images") ||
          pathname.includes(".")
        ) {
          return true;
        }

        // Require authentication for protected routes
        return !!token;
      },
    },
  }
);

// Main middleware function
export async function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;

  // Handle admin routes and admin API routes separately
  if (pathname.startsWith("/admin") || pathname.startsWith("/api/admin")) {
    return handleAdminRoutes(req);
  }

  // For all other routes, use NextAuth middleware
  return userAuthMiddleware(req as any, {} as any);
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (NextAuth.js routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public files (images, etc.)
     */
    "/((?!api/auth|_next/static|_next/image|favicon.ico|images|manifest.json).*)",
  ],
};
