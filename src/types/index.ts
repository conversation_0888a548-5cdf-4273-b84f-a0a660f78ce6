// User types
export interface User {
  id: string;
  email: string;
  name: string;
  role: "ADMIN" | "USER";
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Brand types
export interface Brand {
  id: string;
  name: string;
  description?: string;
  slug: string;
  logo?: string;
  website?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  products?: Product[];
}

// Product types
export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  salePrice?: number;
  images: string[];
  category: Category;
  categoryId: string;
  brand?: Brand;
  brandId?: string;
  stock: number;
  sku: string;
  slug: string;
  featured: boolean;
  status: "ACTIVE" | "INACTIVE" | "OUT_OF_STOCK";
  tags: string[];
  avgRating: number;
  reviewCount: number;
  createdAt: Date;
  updatedAt: Date;
  inventoryEntry?: InventoryEntry;
}

// Category types
export interface Category {
  id: string;
  name: string;
  description?: string;
  slug: string;
  image?: string;
  parentId?: string;
  parent?: Category;
  children?: Category[];
  products?: Product[];
  createdAt: Date;
  updatedAt: Date;
}

// Cart types
export interface CartItem {
  id: string;
  productId: string;
  product: Product;
  quantity: number;
  price: number;
}

export interface Cart {
  id: string;
  userId?: string;
  items: CartItem[];
  total: number;
  createdAt: Date;
  updatedAt: Date;
}

// Order types
export interface Order {
  id: string;
  userId: string;
  user: User;
  items: OrderItem[];
  total: number;
  status:
    | "PENDING"
    | "CONFIRMED"
    | "PROCESSING"
    | "SHIPPED"
    | "DELIVERED"
    | "CANCELLED";
  shippingAddress: Address;
  billingAddress?: Address;
  paymentMethod: "COD" | "BANK_TRANSFER" | "CREDIT_CARD";
  paymentStatus: "PENDING" | "PAID" | "FAILED" | "REFUNDED";
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface OrderItem {
  id: string;
  orderId: string;
  productId: string;
  product: Product;
  quantity: number;
  price: number;
  total: number;
}

// Address types
export interface Address {
  id: string;
  fullName: string;
  phone: string;
  address: string;
  ward: string;
  district: string;
  province: string;
  isDefault: boolean;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Inventory types
export interface InventoryEntry {
  id: string;
  productId: string;
  product?: Product;
  quantity: number;
  reserved: number;
  available: number;
  minStock: number;
  maxStock?: number;
  location?: string;
  createdAt: Date;
  updatedAt: Date;
  stockMovements?: StockMovement[];
}

export interface StockMovement {
  id: string;
  inventoryEntryId: string;
  inventoryEntry?: InventoryEntry;
  type: "IN" | "OUT" | "TRANSFER" | "ADJUSTMENT";
  quantity: number;
  reason?: string;
  reference?: string;
  notes?: string;
  createdBy?: string;
  createdAt: Date;
}

export interface InventoryAlert {
  id: string;
  productId: string;
  product: Product;
  type: "LOW_STOCK" | "OUT_OF_STOCK" | "OVERSTOCK";
  message: string;
  threshold: number;
  currentStock: number;
  createdAt: Date;
}

export interface InventoryStats {
  totalProducts: number;
  totalStock: number;
  lowStockProducts: number;
  outOfStockProducts: number;
  totalValue: number;
  recentMovements: StockMovement[];
  alerts: InventoryAlert[];
}

// Filter and search types
export interface ProductFilters {
  categoryId?: string;
  brandId?: string;
  minPrice?: number;
  maxPrice?: number;
  tags?: string[];
  featured?: boolean;
  status?: Product["status"];
  search?: string;
}

export interface BrandFilters {
  search?: string;
  isActive?: boolean;
}

export interface InventoryFilters {
  search?: string;
  location?: string;
  lowStock?: boolean;
  outOfStock?: boolean;
  brandId?: string;
  categoryId?: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}
