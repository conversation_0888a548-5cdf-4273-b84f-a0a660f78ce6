// TODO: Fix type issues in this file
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore
import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import {
  AdminController,
  PaginationParams,
  SearchParams,
  AdminSession,
  ApiResponse,
} from "../types";
import { withAdminAuth } from "./middleware";
import { ResponseHandler } from "./responses";
import { PaginationHelper } from "./pagination";
import { Error<PERSON>elper, withErrorHandling } from "./errors";

/**
 * Base CRUD controller for admin operations
 */
export abstract class BaseAdminController<T = any>
  implements AdminController<T>
{
  protected model: any;
  protected modelName: string;
  protected searchableFields: string[];
  protected includeRelations: any;

  constructor(
    model: any,
    modelName: string,
    searchableFields: string[] = [],
    includeRelations: any = {}
  ) {
    this.model = model;
    this.modelName = modelName;
    this.searchableFields = searchableFields;
    this.includeRelations = includeRelations;
  }

  /**
   * Validate admin access
   */
  async validateAccess(request: NextRequest): Promise<boolean> {
    try {
      const session = await this.getSession(request);
      return session?.user?.role === "ADMIN";
    } catch {
      return false;
    }
  }

  /**
   * Get session from request
   */
  protected async getSession(
    request: NextRequest
  ): Promise<AdminSession | null> {
    // This would be implemented based on your auth system
    // For now, return null - actual implementation in middleware
    return null;
  }

  /**
   * Handle errors
   */
  handleError(error: any): ApiResponse<any> {
    console.error("Controller error:", error);
    return {
      success: false,
      error: error.message || "An error occurred",
      data: null,
    };
  }

  /**
   * List items with pagination and search
   */
  async list(params: SearchParams & PaginationParams) {
    try {
      const where = PaginationHelper.buildWhere(params, this.searchableFields);
      const orderBy = PaginationHelper.buildOrderBy(params);

      const [items, total] = await Promise.all([
        this.model.findMany({
          where,
          include: this.includeRelations,
          orderBy,
          skip: params.skip,
          take: params.limit,
        }),
        this.model.count({ where }),
      ]);

      const pagination = PaginationHelper.calculate(
        total,
        params.page,
        params.limit
      );

      return ResponseHandler.paginated(items, pagination);
    } catch (error) {
      throw ErrorHelper.database(
        `Lỗi khi lấy danh sách ${this.modelName}`,
        error
      );
    }
  }

  /**
   * Get single item by ID
   */
  async get(id: string) {
    try {
      const item = await this.model.findUnique({
        where: { id },
        include: this.includeRelations,
      });

      if (!item) {
        throw ErrorHelper.notFound(`Không tìm thấy ${this.modelName}`);
      }

      return ResponseHandler.success(item);
    } catch (error) {
      if (error instanceof Error && error.message.includes("Không tìm thấy")) {
        throw error;
      }
      throw ErrorHelper.database(`Lỗi khi lấy ${this.modelName}`, error);
    }
  }

  /**
   * Create new item
   */
  async create(data: Partial<T>) {
    try {
      // Validate data before creation
      await this.validateCreateData(data);

      // Transform data if needed
      const transformedData = await this.transformCreateData(data);

      const item = await this.model.create({
        data: transformedData,
        include: this.includeRelations,
      });

      // Post-creation hook
      await this.afterCreate(item);

      return ResponseHandler.created(item, `Tạo ${this.modelName} thành công`);
    } catch (error) {
      throw ErrorHelper.database(`Lỗi khi tạo ${this.modelName}`, error);
    }
  }

  /**
   * Update existing item
   */
  async update(id: string, data: Partial<T>) {
    try {
      // Check if item exists
      const existingItem = await this.model.findUnique({ where: { id } });
      if (!existingItem) {
        throw ErrorHelper.notFound(`Không tìm thấy ${this.modelName}`);
      }

      // Validate data before update
      await this.validateUpdateData(id, data);

      // Transform data if needed
      const transformedData = await this.transformUpdateData(data);

      const item = await this.model.update({
        where: { id },
        data: transformedData,
        include: this.includeRelations,
      });

      // Post-update hook
      await this.afterUpdate(item, existingItem);

      return ResponseHandler.updated(
        item,
        `Cập nhật ${this.modelName} thành công`
      );
    } catch (error) {
      if (error instanceof Error && error.message.includes("Không tìm thấy")) {
        throw error;
      }
      throw ErrorHelper.database(`Lỗi khi cập nhật ${this.modelName}`, error);
    }
  }

  /**
   * Delete item
   */
  async delete(id: string) {
    try {
      // Check if item exists
      const existingItem = await this.model.findUnique({ where: { id } });
      if (!existingItem) {
        throw ErrorHelper.notFound(`Không tìm thấy ${this.modelName}`);
      }

      // Validate deletion
      await this.validateDelete(id, existingItem);

      // Soft delete if supported, otherwise hard delete
      if (this.supportsSoftDelete()) {
        await this.model.update({
          where: { id },
          data: { deletedAt: new Date() },
        });
      } else {
        await this.model.delete({ where: { id } });
      }

      // Post-deletion hook
      await this.afterDelete(existingItem);

      return ResponseHandler.deleted(`Xóa ${this.modelName} thành công`);
    } catch (error) {
      if (error instanceof Error && error.message.includes("Không tìm thấy")) {
        throw error;
      }
      throw ErrorHelper.database(`Lỗi khi xóa ${this.modelName}`, error);
    }
  }

  /**
   * Bulk operations
   */
  async bulkCreate(items: Partial<T>[]) {
    try {
      const results = await Promise.allSettled(
        items.map((item) => this.create(item))
      );

      const success = results
        .filter((result) => result.status === "fulfilled")
        .map((result) => (result as PromiseFulfilledResult<any>).value);

      const errors = results
        .map((result, index) => ({ result, index }))
        .filter(({ result }) => result.status === "rejected")
        .map(({ result, index }) => ({
          index,
          error: (result as PromiseRejectedResult).reason.message,
          data: items[index],
        }));

      return ResponseHandler.success({ success, errors });
    } catch (error) {
      throw ErrorHelper.database(
        `Lỗi khi tạo hàng loạt ${this.modelName}`,
        error
      );
    }
  }

  /**
   * Bulk delete
   */
  async bulkDelete(ids: string[]) {
    try {
      const results = await Promise.allSettled(
        ids.map((id) => this.delete(id))
      );

      const success = results.filter(
        (result) => result.status === "fulfilled"
      ).length;
      const errors = results.filter(
        (result) => result.status === "rejected"
      ).length;

      return ResponseHandler.success({
        success,
        errors,
        message: `Xóa thành công ${success}/${ids.length} ${this.modelName}`,
      });
    } catch (error) {
      throw ErrorHelper.database(
        `Lỗi khi xóa hàng loạt ${this.modelName}`,
        error
      );
    }
  }

  // Hooks for customization
  protected async validateCreateData(data: Partial<T>): Promise<void> {}
  protected async validateUpdateData(
    id: string,
    data: Partial<T>
  ): Promise<void> {}
  protected async validateDelete(id: string, item: T): Promise<void> {}
  protected async transformCreateData(data: Partial<T>): Promise<any> {
    return data;
  }
  protected async transformUpdateData(data: Partial<T>): Promise<any> {
    return data;
  }
  protected async afterCreate(item: T): Promise<void> {}
  protected async afterUpdate(item: T, oldItem: T): Promise<void> {}
  protected async afterDelete(item: T): Promise<void> {}
  protected supportsSoftDelete(): boolean {
    return false;
  }

  // Route handlers
  async handleList(request: NextRequest) {
    return withErrorHandling(async () => {
      const pagination = PaginationHelper.extract(request);
      const search = PaginationHelper.extractSearch(request);
      return await this.list({ ...pagination, ...search });
    })();
  }

  async handleGet(
    request: NextRequest,
    { params }: { params: { id: string } }
  ) {
    return withErrorHandling(async () => {
      return await this.get(params.id);
    })();
  }

  async handleCreate(request: NextRequest) {
    return withErrorHandling(async () => {
      const data = await request.json();
      return await this.create(data);
    })();
  }

  async handleUpdate(
    request: NextRequest,
    { params }: { params: { id: string } }
  ) {
    return withErrorHandling(async () => {
      const data = await request.json();
      return await this.update(params.id, data);
    })();
  }

  async handleDelete(
    request: NextRequest,
    { params }: { params: { id: string } }
  ) {
    return withErrorHandling(async () => {
      return await this.delete(params.id);
    })();
  }
}
