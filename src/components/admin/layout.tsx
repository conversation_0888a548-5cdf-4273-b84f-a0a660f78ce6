"use client";

import { useState } from "react";
import { SessionProvider } from "next-auth/react";
import { Sidebar } from "./sidebar";
import { AdminHeader } from "./header";

interface AdminLayoutProps {
  children: React.ReactNode;
  title?: string;
}

export function AdminLayout({ children, title }: AdminLayoutProps) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  return (
    <SessionProvider basePath="/api/admin/auth">
      <div className="h-screen flex overflow-hidden">
        {/* Sidebar */}
        <div
          className={`
					fixed inset-y-0 left-0 z-50 lg:static lg:inset-0
					${isSidebarOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"}
					transition-transform duration-300 ease-in-out
				`}
        >
          <Sidebar />
        </div>

        {/* Overlay for mobile */}
        {isSidebarOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={() => setIsSidebarOpen(false)}
          />
        )}

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <AdminHeader
            title={title}
            onMenuClick={() => setIsSidebarOpen(!isSidebarOpen)}
          />
          <main className="flex-1 overflow-auto bg-muted/30 p-6">
            {children}
          </main>
        </div>
      </div>
    </SessionProvider>
  );
}
