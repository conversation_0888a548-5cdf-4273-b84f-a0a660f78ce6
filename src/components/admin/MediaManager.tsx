"use client";

import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import { toast } from "sonner";
import {
  Upload,
  X,
  Image as ImageIcon,
  File,
  Trash2,
  Search,
  Grid,
  List,
  RefreshCw,
} from "lucide-react";

interface MediaFile {
  name: string;
  url: string;
  size: number;
  lastModified: Date;
}

interface MediaManagerProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (url: string) => void;
  selectedUrl?: string;
  folder?: string;
  allowedTypes?: string[];
}

export function MediaManager({
  isOpen,
  onClose,
  onSelect,
  selectedUrl,
  folder = "uploads",
  allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"],
}: MediaManagerProps) {
  const [files, setFiles] = useState<MediaFile[]>([]);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Load files when dialog opens
  useEffect(() => {
    if (isOpen) {
      loadFiles();
    }
  }, [isOpen, folder]);

  const loadFiles = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `/api/admin/media/list?folder=${folder}&limit=100`
      );
      const result = await response.json();

      if (result.success) {
        setFiles(result.data || []);
      } else {
        toast.error(result.error || "Không thể tải danh sách file");
      }
    } catch (error) {
      console.error("Load files error:", error);
      toast.error("Có lỗi xảy ra khi tải danh sách file");
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!allowedTypes.includes(file.type)) {
      toast.error("Loại file không được hỗ trợ");
      return;
    }

    setUploading(true);
    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("folder", folder);

      const response = await fetch("/api/admin/media/upload", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        toast.success("Tải file thành công");
        loadFiles(); // Reload files

        // Auto-select the uploaded file
        onSelect(result.data.url);
      } else {
        toast.error(result.error || "Không thể tải file");
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast.error("Có lỗi xảy ra khi tải file");
    } finally {
      setUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const handleDeleteFile = async (fileUrl: string) => {
    if (!confirm("Bạn có chắc chắn muốn xóa file này?")) {
      return;
    }

    try {
      const response = await fetch(
        `/api/admin/media/delete?url=${encodeURIComponent(fileUrl)}`,
        {
          method: "DELETE",
        }
      );

      const result = await response.json();

      if (result.success) {
        toast.success("Xóa file thành công");
        loadFiles(); // Reload files

        // Clear selection if deleted file was selected
        if (selectedUrl === fileUrl) {
          onSelect("");
        }
      } else {
        toast.error(result.error || "Không thể xóa file");
      }
    } catch (error) {
      console.error("Delete error:", error);
      toast.error("Có lỗi xảy ra khi xóa file");
    }
  };

  const filteredFiles = files.filter((file) =>
    file.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const isImage = (fileName: string) => {
    const imageExtensions = [".jpg", ".jpeg", ".png", ".gif", ".webp"];
    return imageExtensions.some((ext) => fileName.toLowerCase().endsWith(ext));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Quản lý Media</DialogTitle>
        </DialogHeader>

        {/* Toolbar */}
        <div className="flex items-center gap-4 p-4 border-b">
          <Button
            onClick={() => fileInputRef.current?.click()}
            disabled={uploading}
            className="bg-pink-600 hover:bg-pink-700"
          >
            <Upload className="h-4 w-4 mr-2" />
            {uploading ? "Đang tải..." : "Tải lên"}
          </Button>

          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Tìm kiếm file..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === "grid" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("grid")}
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("list")}
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={loadFiles}
              disabled={loading}
            >
              <RefreshCw
                className={`h-4 w-4 ${loading ? "animate-spin" : ""}`}
              />
            </Button>
          </div>
        </div>

        {/* File Grid/List */}
        <div className="flex-1 overflow-auto p-4">
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <RefreshCw className="h-6 w-6 animate-spin" />
              <span className="ml-2">Đang tải...</span>
            </div>
          ) : filteredFiles.length === 0 ? (
            <div className="flex items-center justify-center h-32 text-muted-foreground">
              Không có file nào
            </div>
          ) : viewMode === "grid" ? (
            <div className="grid grid-cols-4 gap-4">
              {filteredFiles.map((file) => (
                <div
                  key={file.url}
                  className={`relative group border rounded-lg p-2 cursor-pointer hover:bg-muted/50 ${
                    selectedUrl === file.url ? "ring-2 ring-pink-500" : ""
                  }`}
                  onClick={() => onSelect(file.url)}
                >
                  <div className="aspect-square bg-muted rounded flex items-center justify-center mb-2">
                    {isImage(file.name) ? (
                      <img
                        src={file.url}
                        alt={file.name}
                        className="w-full h-full object-cover rounded"
                      />
                    ) : (
                      <File className="h-8 w-8 text-muted-foreground" />
                    )}
                  </div>
                  <p className="text-xs truncate" title={file.name}>
                    {file.name}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {formatFileSize(file.size)}
                  </p>

                  <Button
                    variant="destructive"
                    size="sm"
                    className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteFile(file.url);
                    }}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-2">
              {filteredFiles.map((file) => (
                <div
                  key={file.url}
                  className={`flex items-center gap-3 p-3 border rounded-lg cursor-pointer hover:bg-muted/50 ${
                    selectedUrl === file.url ? "ring-2 ring-pink-500" : ""
                  }`}
                  onClick={() => onSelect(file.url)}
                >
                  <div className="w-10 h-10 bg-muted rounded flex items-center justify-center">
                    {isImage(file.name) ? (
                      <ImageIcon className="h-5 w-5 text-muted-foreground" />
                    ) : (
                      <File className="h-5 w-5 text-muted-foreground" />
                    )}
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">{file.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {formatFileSize(file.size)}
                    </p>
                  </div>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteFile(file.url);
                    }}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          accept={allowedTypes.join(",")}
          onChange={handleFileUpload}
          className="hidden"
        />
      </DialogContent>
    </Dialog>
  );
}

// MediaSelector component for easy integration in forms
interface MediaSelectorProps {
  value?: string;
  onChange: (url: string) => void;
  folder?: string;
  allowedTypes?: string[];
  placeholder?: string;
}

export function MediaSelector({
  value,
  onChange,
  folder = "uploads",
  allowedTypes = ["image/jpeg", "image/png", "image/gif", "image/webp"],
  placeholder = "Chọn hình ảnh...",
}: MediaSelectorProps) {
  const [isManagerOpen, setIsManagerOpen] = useState(false);

  const isImage = (url: string) => {
    if (!url) return false;
    const imageExtensions = [".jpg", ".jpeg", ".png", ".gif", ".webp"];
    return imageExtensions.some((ext) => url.toLowerCase().includes(ext));
  };

  return (
    <div className="space-y-2">
      <div className="flex gap-2">
        <Button
          type="button"
          variant="outline"
          onClick={() => setIsManagerOpen(true)}
          className="flex-1"
        >
          <ImageIcon className="h-4 w-4 mr-2" />
          {value ? "Thay đổi" : placeholder}
        </Button>
        {value && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => onChange("")}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {value && isImage(value) && (
        <div className="relative w-full h-32 border rounded-lg overflow-hidden">
          <img
            src={value}
            alt="Selected"
            className="w-full h-full object-cover"
          />
        </div>
      )}

      {value && !isImage(value) && (
        <div className="flex items-center gap-2 p-3 border rounded-lg">
          <File className="h-5 w-5 text-muted-foreground" />
          <span className="text-sm truncate">{value.split("/").pop()}</span>
        </div>
      )}

      <MediaManager
        isOpen={isManagerOpen}
        onClose={() => setIsManagerOpen(false)}
        onSelect={(url) => {
          onChange(url);
          setIsManagerOpen(false);
        }}
        selectedUrl={value}
        folder={folder}
        allowedTypes={allowedTypes}
      />
    </div>
  );
}
