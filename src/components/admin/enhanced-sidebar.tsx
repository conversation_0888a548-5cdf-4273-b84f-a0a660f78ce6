"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Users,
  Settings,
  Tag,
  FileText,
  ChevronLeft,
  ChevronRight,
  ShieldCheck,
  Star,
  MessageSquare,
  Gift,
  Truck,
  CreditCard,
  Bell,
  HelpCircle,
  Database,
  Globe,
  Image,
  Mail,
  Calendar,
  TrendingUp,
  Heart,
  Bookmark,
  UserCheck,
  AlertCircle,
  Home,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { useNotifications } from "@/contexts/NotificationContext";

const menuSections = [
  {
    title: "Tổng quan",
    items: [
      {
        title: "Dashboard",
        href: "/admin",
        icon: LayoutDashboard,
        badge: null,
      },
    ],
  },
  {
    title: "Quản lý sản phẩm",
    items: [
      {
        title: "Sản phẩm",
        href: "/admin/products",
        icon: Package,
        badge: null,
      },
      {
        title: "<PERSON><PERSON> mục",
        href: "/admin/categories",
        icon: Tag,
        badge: null,
      },
      {
        title: "Thương hiệu",
        href: "/admin/brands",
        icon: Star,
        badge: null,
      },
      {
        title: "Thuộc tính",
        href: "/admin/attributes",
        icon: Database,
        badge: null,
      },
      {
        title: "Kho hàng",
        href: "/admin/inventory",
        icon: Home,
        badge: null,
      },
    ],
  },
  {
    title: "Bán hàng",
    items: [
      {
        title: "Đơn hàng",
        href: "/admin/orders",
        icon: ShoppingCart,
        badge: null,
      },
      {
        title: "Thanh toán",
        href: "/admin/payments",
        icon: CreditCard,
        badge: null,
      },
      {
        title: "Vận chuyển",
        href: "/admin/shipping",
        icon: Truck,
        badge: null,
      },
      {
        title: "Khuyến mãi",
        href: "/admin/promotions",
        icon: Gift,
        badge: null,
      },
    ],
  },
  {
    title: "Khách hàng",
    items: [
      {
        title: "Khách hàng",
        href: "/admin/customers",
        icon: Users,
        badge: null,
      },
      {
        title: "Đánh giá",
        href: "/admin/reviews",
        icon: MessageSquare,
        badge: null,
      },
      {
        title: "Yêu thích",
        href: "/admin/wishlists",
        icon: Heart,
        badge: null,
      },
      {
        title: "Hỗ trợ",
        href: "/admin/support",
        icon: HelpCircle,
        badge: null,
      },
    ],
  },
  {
    title: "Nội dung",
    items: [
      {
        title: "Bài viết",
        href: "/admin/posts",
        icon: FileText,
        badge: null,
      },
      {
        title: "Trang",
        href: "/admin/pages",
        icon: Globe,
        badge: null,
      },
      {
        title: "Menu",
        href: "/admin/menus",
        icon: Bookmark,
        badge: null,
      },
      {
        title: "Sự kiện",
        href: "/admin/events",
        icon: Calendar,
        badge: null,
      },
      {
        title: "Thanh toán",
        href: "/admin/payments",
        icon: CreditCard,
        badge: null,
      },
    ],
  },
  {
    title: "Marketing",
    items: [
      {
        title: "Email Marketing",
        href: "/admin/emails",
        icon: Mail,
        badge: null,
      },
      {
        title: "Sự kiện",
        href: "/admin/events",
        icon: Calendar,
        badge: null,
      },
      {
        title: "SEO",
        href: "/admin/seo",
        icon: TrendingUp,
        badge: null,
      },
    ],
  },
  {
    title: "Hệ thống",
    items: [
      {
        title: "Admin Users",
        href: "/admin/admins",
        icon: ShieldCheck,
        badge: null,
        adminOnly: true,
      },
      {
        title: "Thông báo",
        href: "/admin/notifications",
        icon: Bell,
        badge: null,
      },
      {
        title: "Audit Logs",
        href: "/admin/audit-logs",
        icon: AlertCircle,
        badge: null,
        adminOnly: true,
      },
      {
        title: "Thông tin chung",
        href: "/admin/settings",
        icon: Settings,
        badge: null,
        adminOnly: true,
      },
    ],
  },
];

export function EnhancedSidebar() {
  const pathname = usePathname();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const { unreadCount } = useNotifications();

  return (
    <div
      className={cn(
        "flex flex-col h-full bg-white border-r border-gray-200 transition-all duration-300 shadow-sm",
        isCollapsed ? "w-16" : "w-64"
      )}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        {!isCollapsed && (
          <div className="flex items-center space-x-2">
            <div className="h-8 w-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">NS</span>
            </div>
            <h2 className="text-lg font-semibold text-gray-800">Admin Panel</h2>
          </div>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="h-8 w-8 p-0"
        >
          {isCollapsed ? (
            <ChevronRight className="h-4 w-4" />
          ) : (
            <ChevronLeft className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 overflow-y-auto p-4 space-y-6">
        {menuSections.map((section) => (
          <div key={section.title}>
            {!isCollapsed && (
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
                {section.title}
              </h3>
            )}
            <div className="space-y-1">
              {section.items.map((item) => {
                const Icon = item.icon;
                const isActive =
                  pathname === item.href ||
                  (item.href !== "/admin" && pathname.startsWith(item.href));

                return (
                  <Link key={item.href} href={item.href}>
                    <div
                      className={cn(
                        "flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium transition-all group",
                        isActive
                          ? "bg-blue-100 text-blue-700 shadow-sm"
                          : "text-gray-600 hover:bg-gray-100 hover:text-gray-900",
                        isCollapsed && "justify-center"
                      )}
                    >
                      <div className="flex items-center space-x-3">
                        <Icon className="h-5 w-5 flex-shrink-0" />
                        {!isCollapsed && <span>{item.title}</span>}
                      </div>
                      {!isCollapsed &&
                        (item.badge ||
                          (item.title === "Thông báo" && unreadCount > 0)) && (
                          <Badge
                            variant={isActive ? "default" : "secondary"}
                            className="text-xs"
                          >
                            {item.title === "Thông báo" && unreadCount > 0
                              ? unreadCount > 99
                                ? "99+"
                                : unreadCount
                              : item.badge}
                          </Badge>
                        )}
                    </div>
                  </Link>
                );
              })}
            </div>
            {!isCollapsed && <div className="border-t border-gray-200 my-4" />}
          </div>
        ))}
      </nav>
    </div>
  );
}
