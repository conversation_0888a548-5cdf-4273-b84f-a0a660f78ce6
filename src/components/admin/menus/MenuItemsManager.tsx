"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Plus,
  Edit,
  Trash2,
  GripVertical,
  ChevronRight,
  ChevronDown,
  Link as LinkIcon,
  FileText,
  Tag,
  Package,
  Minus,
  ExternalLink,
} from "lucide-react";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";

interface MenuItem {
  id: string;
  menuId: string;
  parentId?: string;
  title: string;
  url?: string;
  type: "LINK" | "PAGE" | "CATEGORY" | "PRODUCT" | "CUSTOM" | "SEPARATOR";
  target?: string;
  icon?: string;
  cssClass?: string;
  order: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  children?: MenuItem[];
  parent?: MenuItem;
}

interface Menu {
  id: string;
  name: string;
  location: string;
  description?: string;
  isActive: boolean;
}

interface MenuItemsManagerProps {
  menu: Menu;
  isOpen: boolean;
  onClose: () => void;
}

const menuItemTypes = [
  { value: "LINK", label: "Liên kết", icon: LinkIcon },
  { value: "PAGE", label: "Trang", icon: FileText },
  { value: "CATEGORY", label: "Danh mục", icon: Tag },
  { value: "PRODUCT", label: "Sản phẩm", icon: Package },
  { value: "CUSTOM", label: "Tùy chỉnh", icon: ExternalLink },
  { value: "SEPARATOR", label: "Phân cách", icon: Minus },
];

export function MenuItemsManager({
  menu,
  isOpen,
  onClose,
}: MenuItemsManagerProps) {
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [isCreateItemOpen, setIsCreateItemOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<MenuItem | null>(null);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  // Fetch menu items
  const fetchMenuItems = async () => {
    if (!menu.id) return;

    setLoading(true);
    try {
      const response = await fetch(`/api/admin/menu-items?menuId=${menu.id}`);
      const data = await response.json();

      if (data.success) {
        setMenuItems(data.data);
      } else {
        toast.error(data.error || "Có lỗi xảy ra khi tải menu items");
      }
    } catch (error) {
      console.error("Error fetching menu items:", error);
      toast.error("Có lỗi xảy ra khi tải menu items");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen && menu.id) {
      fetchMenuItems();
    }
  }, [isOpen, menu.id]);

  // Delete menu item
  const handleDeleteItem = async (item: MenuItem) => {
    if (!confirm(`Bạn có chắc chắn muốn xóa menu item "${item.title}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/menu-items/${item.id}`, {
        method: "DELETE",
      });

      const data = await response.json();

      if (data.success) {
        toast.success("Đã xóa menu item thành công");
        fetchMenuItems();
      } else {
        toast.error(data.error || "Có lỗi xảy ra khi xóa menu item");
      }
    } catch (error) {
      console.error("Error deleting menu item:", error);
      toast.error("Có lỗi xảy ra khi xóa menu item");
    }
  };

  // Toggle expanded state
  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  // Get type icon
  const getTypeIcon = (type: string) => {
    const typeConfig = menuItemTypes.find((t) => t.value === type);
    const IconComponent = typeConfig?.icon || LinkIcon;
    return <IconComponent className="h-4 w-4" />;
  };

  // Render menu item tree
  const renderMenuItem = (item: MenuItem, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.has(item.id);

    return (
      <div key={item.id} className="border rounded-lg p-3 mb-2">
        <div className="flex items-center justify-between">
          <div
            className="flex items-center space-x-3"
            style={{ paddingLeft: `${level * 20}px` }}
          >
            {hasChildren && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => toggleExpanded(item.id)}
                className="p-1 h-6 w-6"
              >
                {isExpanded ? (
                  <ChevronDown className="h-3 w-3" />
                ) : (
                  <ChevronRight className="h-3 w-3" />
                )}
              </Button>
            )}

            <GripVertical className="h-4 w-4 text-muted-foreground cursor-move" />

            <div className="flex items-center space-x-2">
              {getTypeIcon(item.type)}
              <span className="font-medium">{item.title}</span>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Badge variant={item.isActive ? "default" : "secondary"}>
              {item.isActive ? "Hoạt động" : "Tạm dừng"}
            </Badge>
            <Badge variant="outline">{item.type}</Badge>
            {item.url && (
              <Button variant="ghost" size="sm" asChild>
                <a href={item.url} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-3 w-3" />
                </a>
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setEditingItem(item)}
            >
              <Edit className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDeleteItem(item)}
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </div>

        {item.url && (
          <div
            className="mt-2 text-sm text-muted-foreground"
            style={{ paddingLeft: `${level * 20 + 60}px` }}
          >
            URL: {item.url}
          </div>
        )}

        {hasChildren && isExpanded && (
          <div className="mt-3 space-y-2">
            {item.children?.map((child) => renderMenuItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  // Build tree structure
  const buildTree = (items: MenuItem[]): MenuItem[] => {
    const itemMap = new Map<string, MenuItem>();
    const rootItems: MenuItem[] = [];

    // Create map of all items
    items.forEach((item) => {
      itemMap.set(item.id, { ...item, children: [] });
    });

    // Build tree structure
    items.forEach((item) => {
      const currentItem = itemMap.get(item.id)!;

      if (item.parentId) {
        const parent = itemMap.get(item.parentId);
        if (parent) {
          parent.children = parent.children || [];
          parent.children.push(currentItem);
        } else {
          rootItems.push(currentItem);
        }
      } else {
        rootItems.push(currentItem);
      }
    });

    // Sort by order
    const sortByOrder = (items: MenuItem[]) => {
      items.sort((a, b) => a.order - b.order);
      items.forEach((item) => {
        if (item.children) {
          sortByOrder(item.children);
        }
      });
    };

    sortByOrder(rootItems);
    return rootItems;
  };

  const treeItems = buildTree(menuItems);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Quản lý Menu Items - {menu.name}</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Actions */}
          <div className="flex justify-between items-center">
            <div>
              <p className="text-sm text-muted-foreground">
                Vị trí: {menu.location} | Tổng: {menuItems.length} items
              </p>
            </div>
            <Button onClick={() => setIsCreateItemOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Thêm Menu Item
            </Button>
          </div>

          {/* Menu Items Tree */}
          <Card>
            <CardHeader>
              <CardTitle>Danh sách Menu Items</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-8">Đang tải...</div>
              ) : treeItems.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  Chưa có menu item nào
                </div>
              ) : (
                <div className="space-y-2">
                  {treeItems.map((item) => renderMenuItem(item))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Create Menu Item Dialog */}
        <CreateMenuItemDialog
          menuId={menu.id}
          isOpen={isCreateItemOpen}
          onClose={() => setIsCreateItemOpen(false)}
          onSuccess={() => {
            fetchMenuItems();
            setIsCreateItemOpen(false);
          }}
          parentOptions={menuItems.filter((item) => !item.parentId)} // Only root items can be parents
        />

        {/* Edit Menu Item Dialog */}
        {editingItem && (
          <EditMenuItemDialog
            menuItem={editingItem}
            isOpen={!!editingItem}
            onClose={() => setEditingItem(null)}
            onSuccess={() => {
              fetchMenuItems();
              setEditingItem(null);
            }}
            parentOptions={menuItems.filter(
              (item) => !item.parentId && item.id !== editingItem.id
            )}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}

// Create Menu Item Dialog Component
function CreateMenuItemDialog({
  menuId,
  isOpen,
  onClose,
  onSuccess,
  parentOptions,
}: {
  menuId: string;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  parentOptions: MenuItem[];
}) {
  const [formData, setFormData] = useState({
    title: "",
    url: "",
    type: "LINK" as const,
    target: "default",
    icon: "",
    cssClass: "",
    parentId: "none",
    order: 0,
    isActive: true,
  });
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch("/api/admin/menu-items", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          menuId,
          parentId:
            formData.parentId === "none" ? undefined : formData.parentId,
          target: formData.target === "default" ? "" : formData.target,
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success("Đã tạo menu item thành công");
        onSuccess();
        setFormData({
          title: "",
          url: "",
          type: "LINK",
          target: "default",
          icon: "",
          cssClass: "",
          parentId: "none",
          order: 0,
          isActive: true,
        });
      } else {
        toast.error(data.error || "Có lỗi xảy ra khi tạo menu item");
      }
    } catch (error) {
      console.error("Error creating menu item:", error);
      toast.error("Có lỗi xảy ra khi tạo menu item");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Tạo Menu Item Mới</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="title">Tiêu đề *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) =>
                setFormData({ ...formData, title: e.target.value })
              }
              placeholder="Nhập tiêu đề menu item..."
              required
            />
          </div>

          <div>
            <Label htmlFor="type">Loại *</Label>
            <Select
              value={formData.type}
              onValueChange={(value: any) =>
                setFormData({ ...formData, type: value })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {menuItemTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    <div className="flex items-center space-x-2">
                      <type.icon className="h-4 w-4" />
                      <span>{type.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {formData.type !== "SEPARATOR" && (
            <div>
              <Label htmlFor="url">URL</Label>
              <Input
                id="url"
                value={formData.url}
                onChange={(e) =>
                  setFormData({ ...formData, url: e.target.value })
                }
                placeholder="Nhập URL..."
              />
            </div>
          )}

          <div>
            <Label htmlFor="parent">Menu Item Cha</Label>
            <Select
              value={formData.parentId}
              onValueChange={(value) =>
                setFormData({ ...formData, parentId: value })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Chọn menu item cha (tùy chọn)" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">Không có</SelectItem>
                {parentOptions.map((option) => (
                  <SelectItem key={option.id} value={option.id}>
                    {option.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="order">Thứ tự</Label>
              <Input
                id="order"
                type="number"
                value={formData.order}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    order: parseInt(e.target.value) || 0,
                  })
                }
                min="0"
              />
            </div>
            <div>
              <Label htmlFor="target">Target</Label>
              <Select
                value={formData.target}
                onValueChange={(value) =>
                  setFormData({ ...formData, target: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn target" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="default">Mặc định</SelectItem>
                  <SelectItem value="_blank">Cửa sổ mới</SelectItem>
                  <SelectItem value="_self">Cùng cửa sổ</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) =>
                setFormData({ ...formData, isActive: !!checked })
              }
            />
            <Label htmlFor="isActive">Kích hoạt menu item</Label>
          </div>

          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Hủy
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? "Đang tạo..." : "Tạo Menu Item"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}

// Edit Menu Item Dialog Component
function EditMenuItemDialog({
  menuItem,
  isOpen,
  onClose,
  onSuccess,
  parentOptions,
}: {
  menuItem: MenuItem;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  parentOptions: MenuItem[];
}) {
  const [formData, setFormData] = useState({
    title: menuItem.title,
    url: menuItem.url || "",
    type: menuItem.type,
    target: menuItem.target || "default",
    icon: menuItem.icon || "",
    cssClass: menuItem.cssClass || "",
    parentId: menuItem.parentId || "none",
    order: menuItem.order,
    isActive: menuItem.isActive,
  });
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch(`/api/admin/menu-items/${menuItem.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...formData,
          parentId:
            formData.parentId === "none" ? undefined : formData.parentId,
          target: formData.target === "default" ? "" : formData.target,
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success("Đã cập nhật menu item thành công");
        onSuccess();
      } else {
        toast.error(data.error || "Có lỗi xảy ra khi cập nhật menu item");
      }
    } catch (error) {
      console.error("Error updating menu item:", error);
      toast.error("Có lỗi xảy ra khi cập nhật menu item");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Chỉnh sửa Menu Item</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="edit-title">Tiêu đề *</Label>
            <Input
              id="edit-title"
              value={formData.title}
              onChange={(e) =>
                setFormData({ ...formData, title: e.target.value })
              }
              placeholder="Nhập tiêu đề menu item..."
              required
            />
          </div>

          <div>
            <Label htmlFor="edit-type">Loại *</Label>
            <Select
              value={formData.type}
              onValueChange={(value: any) =>
                setFormData({ ...formData, type: value })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {menuItemTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    <div className="flex items-center space-x-2">
                      <type.icon className="h-4 w-4" />
                      <span>{type.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {formData.type !== "SEPARATOR" && (
            <div>
              <Label htmlFor="edit-url">URL</Label>
              <Input
                id="edit-url"
                value={formData.url}
                onChange={(e) =>
                  setFormData({ ...formData, url: e.target.value })
                }
                placeholder="Nhập URL..."
              />
            </div>
          )}

          <div>
            <Label htmlFor="edit-parent">Menu Item Cha</Label>
            <Select
              value={formData.parentId}
              onValueChange={(value) =>
                setFormData({ ...formData, parentId: value })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Chọn menu item cha (tùy chọn)" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">Không có</SelectItem>
                {parentOptions.map((option) => (
                  <SelectItem key={option.id} value={option.id}>
                    {option.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="edit-order">Thứ tự</Label>
              <Input
                id="edit-order"
                type="number"
                value={formData.order}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    order: parseInt(e.target.value) || 0,
                  })
                }
                min="0"
              />
            </div>
            <div>
              <Label htmlFor="edit-target">Target</Label>
              <Select
                value={formData.target}
                onValueChange={(value) =>
                  setFormData({ ...formData, target: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Chọn target" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="default">Mặc định</SelectItem>
                  <SelectItem value="_blank">Cửa sổ mới</SelectItem>
                  <SelectItem value="_self">Cùng cửa sổ</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="edit-isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) =>
                setFormData({ ...formData, isActive: !!checked })
              }
            />
            <Label htmlFor="edit-isActive">Kích hoạt menu item</Label>
          </div>

          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Hủy
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? "Đang cập nhật..." : "Cập nhật Menu Item"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
