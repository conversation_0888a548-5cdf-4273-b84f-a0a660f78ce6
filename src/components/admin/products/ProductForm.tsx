"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import {
  Package,
  DollarSign,
  Image as ImageIcon,
  Settings,
  Tag,
  Save,
  ArrowLeft,
  AlertCircle,
  CheckCircle,
  X,
  Plus,
} from "lucide-react";
import { ProductImageManager } from "./ProductImageManager";
import { ProductAttributeManager } from "./ProductAttributeManager";

interface ProductImage {
  url: string;
  type: "INTERNAL" | "EXTERNAL";
  externalUrl?: string;
}

interface ProductFormData {
  name: string;
  description: string;
  price: number;
  salePrice?: number | null;
  sku: string;
  stock: number;
  categoryId: string;
  brandId?: string;
  images: ProductImage[];
  featured: boolean;
  status: "ACTIVE" | "INACTIVE" | "OUT_OF_STOCK";
  tags: string[];
  attributes: Array<{ attributeId: string; attributeValueId: string }>;
}

interface Category {
  id: string;
  name: string;
}

interface Brand {
  id: string;
  name: string;
}

interface ProductFormProps {
  initialData?: Partial<ProductFormData>;
  mode: "create" | "edit";
  productId?: string;
  onSubmit: (data: ProductFormData) => Promise<void>;
  loading?: boolean;
}

export function ProductForm({
  initialData,
  mode,
  productId,
  onSubmit,
  loading = false,
}: ProductFormProps) {
  const router = useRouter();
  const [categories, setCategories] = useState<Category[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [tagInput, setTagInput] = useState("");
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  const [formData, setFormData] = useState<ProductFormData>({
    name: "",
    description: "",
    price: 0,
    salePrice: null,
    sku: "",
    stock: 0,
    categoryId: "",
    brandId: "",
    images: [],
    featured: false,
    status: "ACTIVE",
    tags: [],
    attributes: [],
    ...initialData,
  });

  useEffect(() => {
    fetchCategories();
    fetchBrands();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await fetch("/api/admin/categories");
      if (response.ok) {
        const data = await response.json();
        setCategories(data.categories || []);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
    }
  };

  const fetchBrands = async () => {
    try {
      const response = await fetch("/api/admin/brands");
      if (response.ok) {
        const data = await response.json();
        setBrands(data.brands || []);
      }
    } catch (error) {
      console.error("Error fetching brands:", error);
    }
  };

  const handleInputChange = (field: keyof ProductFormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (formErrors[field]) {
      setFormErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const handleAttributesChange = (
    attributes: Array<{ attributeId: string; attributeValueId: string }>
  ) => {
    setFormData((prev) => ({ ...prev, attributes }));
  };

  const addTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData((prev) => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()],
      }));
      setTagInput("");
    }
  };

  const removeTag = (tag: string) => {
    setFormData((prev) => ({
      ...prev,
      tags: prev.tags.filter((t) => t !== tag),
    }));
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};

    // Basic validation
    if (!formData.name.trim()) {
      errors.name = "Tên sản phẩm là bắt buộc";
    } else if (formData.name.length < 3) {
      errors.name = "Tên sản phẩm phải có ít nhất 3 ký tự";
    } else if (formData.name.length > 255) {
      errors.name = "Tên sản phẩm không được vượt quá 255 ký tự";
    }

    if (!formData.description.trim()) {
      errors.description = "Mô tả sản phẩm là bắt buộc";
    } else if (formData.description.length < 10) {
      errors.description = "Mô tả sản phẩm phải có ít nhất 10 ký tự";
    }

    if (!formData.categoryId) {
      errors.categoryId = "Vui lòng chọn danh mục";
    }

    if (formData.price <= 0) {
      errors.price = "Giá sản phẩm phải lớn hơn 0";
    } else if (formData.price > 999999999) {
      errors.price = "Giá sản phẩm không được vượt quá 999,999,999 VND";
    }

    if (formData.salePrice && formData.salePrice >= formData.price) {
      errors.salePrice = "Giá khuyến mãi phải nhỏ hơn giá gốc";
    } else if (formData.salePrice && formData.salePrice <= 0) {
      errors.salePrice = "Giá khuyến mãi phải lớn hơn 0";
    }

    if (formData.stock < 0) {
      errors.stock = "Số lượng tồn kho không được âm";
    }

    if (formData.images.length === 0) {
      errors.images = "Vui lòng thêm ít nhất một hình ảnh";
    } else if (formData.images.length > 10) {
      errors.images = "Không được thêm quá 10 hình ảnh";
    }

    // SKU validation
    if (formData.sku && formData.sku.length > 100) {
      errors.sku = "SKU không được vượt quá 100 ký tự";
    }

    // Tags validation
    if (formData.tags.length > 20) {
      errors.tags = "Không được thêm quá 20 tags";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("Vui lòng kiểm tra lại thông tin form");
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error("Submit error:", error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return "bg-green-100 text-green-800 border-green-200";
      case "INACTIVE":
        return "bg-gray-100 text-gray-800 border-gray-200";
      case "OUT_OF_STOCK":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
            className="hover:bg-gray-100"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Quay lại
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {mode === "create" ? "Tạo sản phẩm mới" : "Chỉnh sửa sản phẩm"}
            </h1>
            <p className="text-gray-600 mt-1">
              {mode === "create"
                ? "Thêm sản phẩm mới vào cửa hàng của bạn"
                : "Cập nhật thông tin sản phẩm"}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Badge className={getStatusColor(formData.status)}>
            {formData.status === "ACTIVE" && "Đang bán"}
            {formData.status === "INACTIVE" && "Tạm ngưng"}
            {formData.status === "OUT_OF_STOCK" && "Hết hàng"}
          </Badge>
          <Button
            type="submit"
            form="product-form"
            disabled={loading}
            className="bg-pink-600 hover:bg-pink-700 text-white"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Đang lưu...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                {mode === "create" ? "Tạo sản phẩm" : "Cập nhật"}
              </>
            )}
          </Button>
        </div>
      </div>

      <form
        id="product-form"
        onSubmit={handleSubmit}
        className="grid grid-cols-1 lg:grid-cols-3 gap-8"
      >
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-8">
          {/* Basic Information */}
          <Card className="border-l-4 border-l-pink-500">
            <CardHeader className="bg-gradient-to-r from-pink-50 to-transparent">
              <CardTitle className="flex items-center text-pink-900">
                <Package className="h-5 w-5 mr-2" />
                Thông tin cơ bản
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6 pt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <Label htmlFor="name" className="text-sm font-medium">
                    Tên sản phẩm *
                  </Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="Nhập tên sản phẩm"
                    className={`mt-2 ${formErrors.name ? "border-red-500" : ""}`}
                  />
                  {formErrors.name && (
                    <p className="text-red-500 text-sm mt-1 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {formErrors.name}
                    </p>
                  )}
                </div>

                <div className="md:col-span-2">
                  <Label htmlFor="description" className="text-sm font-medium">
                    Mô tả sản phẩm *
                  </Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) =>
                      handleInputChange("description", e.target.value)
                    }
                    placeholder="Nhập mô tả chi tiết sản phẩm"
                    rows={4}
                    className={`mt-2 ${formErrors.description ? "border-red-500" : ""}`}
                  />
                  {formErrors.description && (
                    <p className="text-red-500 text-sm mt-1 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {formErrors.description}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="sku" className="text-sm font-medium">
                    SKU
                  </Label>
                  <Input
                    id="sku"
                    value={formData.sku}
                    onChange={(e) => handleInputChange("sku", e.target.value)}
                    placeholder="Mã sản phẩm (tự động tạo nếu để trống)"
                    className={`mt-2 ${formErrors.sku ? "border-red-500" : ""}`}
                  />
                  {formErrors.sku && (
                    <p className="text-red-500 text-sm mt-1 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {formErrors.sku}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="stock" className="text-sm font-medium">
                    Số lượng tồn kho
                  </Label>
                  <Input
                    id="stock"
                    type="number"
                    value={formData.stock}
                    onChange={(e) =>
                      handleInputChange("stock", parseInt(e.target.value) || 0)
                    }
                    placeholder="0"
                    min="0"
                    className={`mt-2 ${formErrors.stock ? "border-red-500" : ""}`}
                  />
                  {formErrors.stock && (
                    <p className="text-red-500 text-sm mt-1 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {formErrors.stock}
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Pricing */}
          <Card className="border-l-4 border-l-green-500">
            <CardHeader className="bg-gradient-to-r from-green-50 to-transparent">
              <CardTitle className="flex items-center text-green-900">
                <DollarSign className="h-5 w-5 mr-2" />
                Giá bán
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6 pt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="price" className="text-sm font-medium">
                    Giá gốc (VND) *
                  </Label>
                  <Input
                    id="price"
                    type="number"
                    value={formData.price}
                    onChange={(e) =>
                      handleInputChange(
                        "price",
                        parseFloat(e.target.value) || 0
                      )
                    }
                    placeholder="0"
                    min="0"
                    className={`mt-2 ${formErrors.price ? "border-red-500" : ""}`}
                  />
                  {formErrors.price && (
                    <p className="text-red-500 text-sm mt-1 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {formErrors.price}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="salePrice" className="text-sm font-medium">
                    Giá khuyến mãi (VND)
                  </Label>
                  <Input
                    id="salePrice"
                    type="number"
                    value={formData.salePrice || ""}
                    onChange={(e) =>
                      handleInputChange(
                        "salePrice",
                        e.target.value ? parseFloat(e.target.value) : null
                      )
                    }
                    placeholder="Để trống nếu không có khuyến mãi"
                    min="0"
                    className={`mt-2 ${formErrors.salePrice ? "border-red-500" : ""}`}
                  />
                  {formErrors.salePrice && (
                    <p className="text-red-500 text-sm mt-1 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {formErrors.salePrice}
                    </p>
                  )}
                </div>
              </div>

              {/* Price Preview */}
              {formData.price > 0 && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">
                    Xem trước giá:
                  </h4>
                  <div className="flex items-center gap-3">
                    {formData.salePrice &&
                    formData.salePrice < formData.price ? (
                      <>
                        <span className="text-2xl font-bold text-green-600">
                          {formData.salePrice.toLocaleString("vi-VN")}₫
                        </span>
                        <span className="text-lg text-gray-500 line-through">
                          {formData.price.toLocaleString("vi-VN")}₫
                        </span>
                        <Badge variant="destructive" className="text-xs">
                          -
                          {Math.round(
                            ((formData.price - formData.salePrice) /
                              formData.price) *
                              100
                          )}
                          %
                        </Badge>
                      </>
                    ) : (
                      <span className="text-2xl font-bold text-gray-900">
                        {formData.price.toLocaleString("vi-VN")}₫
                      </span>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Images */}
          <Card className="border-l-4 border-l-blue-500">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-transparent">
              <CardTitle className="flex items-center text-blue-900">
                <ImageIcon className="h-5 w-5 mr-2" />
                Hình ảnh sản phẩm
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-6">
              <ProductImageManager
                images={formData.images}
                onChange={(images) => handleInputChange("images", images)}
              />
              {formErrors.images && (
                <p className="text-red-500 text-sm mt-2 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {formErrors.images}
                </p>
              )}
            </CardContent>
          </Card>

          {/* Product Attributes */}
          <ProductAttributeManager
            attributes={formData.attributes}
            onAttributesChange={handleAttributesChange}
          />

          {/* Attributes Validation Errors */}
          {formErrors.attributes && (
            <Card className="border-red-200 bg-red-50">
              <CardContent className="pt-6">
                <p className="text-red-600 text-sm flex items-center">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  {formErrors.attributes}
                </p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Settings */}
          <Card className="border-l-4 border-l-purple-500">
            <CardHeader className="bg-gradient-to-r from-purple-50 to-transparent">
              <CardTitle className="flex items-center text-purple-900">
                <Settings className="h-5 w-5 mr-2" />
                Cài đặt
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6 pt-6">
              <div>
                <Label htmlFor="categoryId" className="text-sm font-medium">
                  Danh mục *
                </Label>
                <Select
                  value={formData.categoryId}
                  onValueChange={(value) =>
                    handleInputChange("categoryId", value)
                  }
                >
                  <SelectTrigger
                    className={`mt-2 ${formErrors.categoryId ? "border-red-500" : ""}`}
                  >
                    <SelectValue placeholder="Chọn danh mục" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {formErrors.categoryId && (
                  <p className="text-red-500 text-sm mt-1 flex items-center">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {formErrors.categoryId}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="brandId" className="text-sm font-medium">
                  Thương hiệu
                </Label>
                <Select
                  value={formData.brandId || "none"}
                  onValueChange={(value) =>
                    handleInputChange("brandId", value === "none" ? "" : value)
                  }
                >
                  <SelectTrigger className="mt-2">
                    <SelectValue placeholder="Chọn thương hiệu (tùy chọn)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">Không chọn thương hiệu</SelectItem>
                    {brands.map((brand) => (
                      <SelectItem key={brand.id} value={brand.id}>
                        {brand.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="status" className="text-sm font-medium">
                  Trạng thái
                </Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => handleInputChange("status", value)}
                >
                  <SelectTrigger className="mt-2">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ACTIVE">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        Đang bán
                      </div>
                    </SelectItem>
                    <SelectItem value="INACTIVE">
                      <div className="flex items-center gap-2">
                        <X className="h-4 w-4 text-gray-500" />
                        Tạm ngưng
                      </div>
                    </SelectItem>
                    <SelectItem value="OUT_OF_STOCK">
                      <div className="flex items-center gap-2">
                        <AlertCircle className="h-4 w-4 text-red-500" />
                        Hết hàng
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="featured" className="text-sm font-medium">
                    Sản phẩm nổi bật
                  </Label>
                  <p className="text-xs text-gray-500 mt-1">
                    Hiển thị sản phẩm ở vị trí nổi bật
                  </p>
                </div>
                <Switch
                  id="featured"
                  checked={formData.featured}
                  onCheckedChange={(checked) =>
                    handleInputChange("featured", checked)
                  }
                />
              </div>
            </CardContent>
          </Card>

          {/* Tags */}
          <Card className="border-l-4 border-l-orange-500">
            <CardHeader className="bg-gradient-to-r from-orange-50 to-transparent">
              <CardTitle className="flex items-center text-orange-900">
                <Tag className="h-5 w-5 mr-2" />
                Tags
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 pt-6">
              <div>
                <Label htmlFor="tagInput" className="text-sm font-medium">
                  Thêm tag
                </Label>
                <div className="flex gap-2 mt-2">
                  <Input
                    id="tagInput"
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    placeholder="Nhập tag..."
                    onKeyPress={(e) => {
                      if (e.key === "Enter") {
                        e.preventDefault();
                        addTag();
                      }
                    }}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addTag}
                    disabled={!tagInput.trim()}
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {formData.tags.length > 0 && (
                <div>
                  <Label className="text-sm font-medium">Tags hiện tại</Label>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {formData.tags.map((tag, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="flex items-center gap-1 px-2 py-1"
                      >
                        {tag}
                        <button
                          type="button"
                          onClick={() => removeTag(tag)}
                          className="text-gray-500 hover:text-red-500 ml-1"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {formErrors.tags && (
                <p className="text-red-500 text-sm flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {formErrors.tags}
                </p>
              )}
            </CardContent>
          </Card>
        </div>
      </form>
    </div>
  );
}
