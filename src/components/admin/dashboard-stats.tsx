"use client";

import { useState, useEffect } from "react";
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Loader2,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency, formatNumber } from "@/lib/utils";

interface StatsData {
  overview: {
    totalRevenue: { value: number; growth: number };
    totalOrders: { value: number; growth: number };
    totalUsers: { value: number; growth: number };
    totalProducts: { value: number; growth: number };
  };
}

export function DashboardStats() {
  const [stats, setStats] = useState<StatsData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Only fetch stats if we're actually on the dashboard page
    if (
      typeof window !== "undefined" &&
      window.location.pathname === "/admin"
    ) {
      fetchStats();
    } else {
      setLoading(false);
    }
  }, []);

  const fetchStats = async () => {
    try {
      const response = await fetch("/api/admin/stats");
      if (response.ok) {
        const data = await response.json();
        console.log("Stats API response:", data);

        // Validate response structure
        if (
          data &&
          data.overview &&
          data.overview.totalRevenue &&
          data.overview.totalOrders &&
          data.overview.totalUsers &&
          data.overview.totalProducts
        ) {
          setStats(data);
        } else {
          console.error("Invalid stats response structure:", data);
        }
      } else {
        console.error("Stats API error:", response.status, response.statusText);
      }
    } catch (error) {
      console.error("Error fetching stats:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        data-testid="stats-loading"
      >
        {Array.from({ length: 4 }, (_, i) => (
          <Card key={i} className="relative overflow-hidden">
            <CardContent className="p-6">
              <div className="flex items-center justify-center h-20">
                <Loader2 className="h-6 w-6 animate-spin" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!stats) {
    return (
      <div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        data-testid="stats-error"
      >
        {Array.from({ length: 4 }, (_, i) => (
          <Card key={i} className="relative overflow-hidden">
            <CardContent className="p-6">
              <div className="text-center text-muted-foreground">
                Không thể tải dữ liệu
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const statsConfig = [
    {
      title: "Tổng doanh thu",
      value: stats?.overview?.totalRevenue?.value || 0,
      change: stats?.overview?.totalRevenue?.growth || 0,
      icon: DollarSign,
      color: "text-green-600",
      bgColor: "bg-green-100",
      format: "currency",
      testId: "revenue-stat",
    },
    {
      title: "Đơn hàng",
      value: stats?.overview?.totalOrders?.value || 0,
      change: stats?.overview?.totalOrders?.growth || 0,
      icon: ShoppingCart,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      format: "number",
      testId: "orders-stat",
    },
    {
      title: "Khách hàng",
      value: stats?.overview?.totalUsers?.value || 0,
      change: stats?.overview?.totalUsers?.growth || 0,
      icon: Users,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
      format: "number",
      testId: "users-stat",
    },
    {
      title: "Sản phẩm",
      value: stats?.overview?.totalProducts?.value || 0,
      change: stats?.overview?.totalProducts?.growth || 0,
      icon: Package,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
      format: "number",
      testId: "products-stat",
    },
  ];
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statsConfig.map((stat) => (
        <Card
          key={stat.title}
          className="relative overflow-hidden"
          data-testid="stats-card"
        >
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {stat.title}
            </CardTitle>
            <div className={`p-2 rounded-lg ${stat.bgColor}`}>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2" data-testid={stat.testId}>
              <div className="text-2xl font-bold stat-value">
                {stat.format === "currency"
                  ? formatCurrency(stat.value)
                  : formatNumber(stat.value)}
              </div>
              <div className="flex items-center space-x-1 text-sm">
                {stat.change >= 0 ? (
                  <TrendingUp className="h-4 w-4 text-green-600" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-600" />
                )}
                <span
                  className={
                    stat.change >= 0 ? "text-green-600" : "text-red-600"
                  }
                >
                  {Math.abs(stat.change)}%
                </span>
                <span className="text-muted-foreground">
                  so với tháng trước
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
