"use client";

import { NotificationBell } from "@/components/admin/notifications/NotificationBell";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAdminAuth } from "@/contexts/AdminAuthContext";
import {
  HelpCircle,
  Lock,
  LogOut,
  Mail,
  Menu,
  Search,
  Settings,
  Shield,
  User,
  UserCog,
} from "lucide-react";
import Link from "next/link";

interface EnhancedAdminHeaderProps {
  onMenuClick?: () => void;
  title?: string;
}

export function EnhancedAdminHeader({
  onMenuClick,
  title = "Dashboard",
}: EnhancedAdminHeaderProps) {
  const { logout, adminUser } = useAdminAuth();

  const handleLogout = async () => {
    await logout();
  };

  return (
    <header className="h-16 bg-white border-b border-gray-200 flex items-center justify-between px-6 shadow-sm">
      {/* Left side */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={onMenuClick}
          className="lg:hidden"
        >
          <Menu className="h-5 w-5" />
        </Button>
        <h1 className="text-xl font-semibold text-gray-800">{title}</h1>
      </div>

      {/* Center - Search */}
      <div className="hidden md:flex items-center flex-1 max-w-md mx-8">
        <div className="relative w-full">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Tìm kiếm sản phẩm, đơn hàng, khách hàng..."
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg bg-gray-50 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Right side */}
      <div className="flex items-center gap-3">
        {/* Quick Search for mobile */}
        <Button variant="ghost" size="sm" className="md:hidden">
          <Search className="h-5 w-5" />
        </Button>

        {/* Notifications */}
        <NotificationBell size="sm" variant="ghost" />

        {/* User Profile Menu */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="flex items-center gap-2 h-10 px-3"
            >
              <Avatar className="h-8 w-8">
                <AvatarImage
                  src={adminUser?.avatar}
                  alt={adminUser?.name || "Admin"}
                />
                <AvatarFallback className="bg-blue-500 text-white">
                  {adminUser?.name?.charAt(0).toUpperCase() || "AD"}
                </AvatarFallback>
              </Avatar>
              <div className="hidden sm:flex flex-col items-start">
                <span className="text-sm font-medium">
                  {adminUser?.name || "Admin User"}
                </span>
                <span className="text-xs text-gray-500">
                  {adminUser?.role || "Administrator"}
                </span>
              </div>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium leading-none">
                  {adminUser?.name || "Admin User"}
                </p>
                <p className="text-xs leading-none text-muted-foreground">
                  {adminUser?.email || "<EMAIL>"}
                </p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />

            <DropdownMenuGroup>
              <DropdownMenuItem asChild>
                <Link href="/admin/profile" className="flex items-center">
                  <User className="mr-2 h-4 w-4" />
                  <span>Thông tin cá nhân</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/admin/profile" className="flex items-center">
                  <UserCog className="mr-2 h-4 w-4" />
                  <span>Cập nhật hồ sơ</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/admin/profile" className="flex items-center">
                  <Lock className="mr-2 h-4 w-4" />
                  <span>Đổi mật khẩu</span>
                </Link>
              </DropdownMenuItem>
            </DropdownMenuGroup>

            <DropdownMenuSeparator />

            <DropdownMenuGroup>
              <DropdownMenuItem asChild>
                <Link href="/admin/settings" className="flex items-center">
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Cài đặt</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/admin/profile" className="flex items-center">
                  <Shield className="mr-2 h-4 w-4" />
                  <span>Bảo mật</span>
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/admin/messages" className="flex items-center">
                  <Mail className="mr-2 h-4 w-4" />
                  <span>Tin nhắn</span>
                </Link>
              </DropdownMenuItem>
            </DropdownMenuGroup>

            <DropdownMenuSeparator />

            <DropdownMenuItem asChild>
              <Link href="/admin/help" className="flex items-center">
                <HelpCircle className="mr-2 h-4 w-4" />
                <span>Trợ giúp & Hỗ trợ</span>
              </Link>
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            <DropdownMenuItem
              onClick={handleLogout}
              className="text-red-600"
              data-testid="logout-button"
            >
              <LogOut className="mr-2 h-4 w-4" />
              <span>Đăng xuất</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
}
