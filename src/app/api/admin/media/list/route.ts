import { NextRequest, NextResponse } from 'next/server';
import { verifyAdminToken } from '@/lib/admin-auth';
import { listFiles } from '@/lib/minio';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: 'Không có quyền truy cập' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const folder = searchParams.get('folder') || 'uploads';
    const limit = parseInt(searchParams.get('limit') || '50');

    // List files from MinIO
    const result = await listFiles(folder, limit);

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.files,
      });
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('List files error:', error);
    return NextResponse.json(
      { success: false, error: '<PERSON><PERSON> lỗi xảy ra khi tải danh sách file' },
      { status: 500 }
    );
  }
}
