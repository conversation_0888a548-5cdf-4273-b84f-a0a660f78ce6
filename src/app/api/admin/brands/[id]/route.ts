import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// Validation schema for brand update
const brandUpdateSchema = z.object({
  name: z.string().min(1, "<PERSON><PERSON><PERSON> thương hiệu là bắt buộc").optional(),
  description: z.string().optional(),
  slug: z.string().min(1, "Slug là bắt buộc").optional(),
  logo: z.string().optional(),
  website: z.string().url("Website phải là URL hợp lệ").optional().or(z.literal("")),
  isActive: z.boolean().optional(),
});

// GET /api/admin/brands/[id] - Get single brand
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const brand = await prisma.brand.findUnique({
      where: { id: params.id },
      include: {
        products: {
          select: {
            id: true,
            name: true,
            slug: true,
            images: true,
            price: true,
            stock: true,
            status: true,
          },
          take: 10, // Limit to 10 products for preview
        },
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    if (!brand) {
      return NextResponse.json(
        { success: false, error: "Không tìm thấy thương hiệu" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: brand,
    });
  } catch (error) {
    console.error("Get brand error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy thông tin thương hiệu" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/brands/[id] - Update brand
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    
    // Validate input
    const validatedData = brandUpdateSchema.parse(body);

    // Check if brand exists
    const existingBrand = await prisma.brand.findUnique({
      where: { id: params.id },
    });

    if (!existingBrand) {
      return NextResponse.json(
        { success: false, error: "Không tìm thấy thương hiệu" },
        { status: 404 }
      );
    }

    // Check if slug already exists (if updating slug)
    if (validatedData.slug && validatedData.slug !== existingBrand.slug) {
      const slugExists = await prisma.brand.findUnique({
        where: { slug: validatedData.slug },
      });

      if (slugExists) {
        return NextResponse.json(
          { success: false, error: "Slug đã tồn tại" },
          { status: 400 }
        );
      }
    }

    // Update brand
    const brand = await prisma.brand.update({
      where: { id: params.id },
      data: validatedData,
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: brand,
      message: "Thương hiệu đã được cập nhật thành công",
    });
  } catch (error) {
    console.error("Update brand error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi cập nhật thương hiệu" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/brands/[id] - Delete brand
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if brand exists
    const existingBrand = await prisma.brand.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    if (!existingBrand) {
      return NextResponse.json(
        { success: false, error: "Không tìm thấy thương hiệu" },
        { status: 404 }
      );
    }

    // Check if brand has products
    if (existingBrand._count.products > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Không thể xóa thương hiệu vì còn ${existingBrand._count.products} sản phẩm đang sử dụng` 
        },
        { status: 400 }
      );
    }

    // Delete brand
    await prisma.brand.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      success: true,
      message: "Thương hiệu đã được xóa thành công",
    });
  } catch (error) {
    console.error("Delete brand error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi xóa thương hiệu" },
      { status: 500 }
    );
  }
}
