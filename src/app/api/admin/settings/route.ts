import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";

// Default settings structure
const DEFAULT_SETTINGS = {
  // General settings
  siteName: "NS Shop",
  siteDescription: "Cửa hàng thời trang trực tuyến",
  siteUrl: "https://nsshop.com",
  logo: "",
  favicon: "",

  // Contact settings
  contactEmail: "<EMAIL>",
  contactPhone: "**********",
  address: "123 Đường ABC, Quận 1, TP.HCM",

  // Social media
  socialMedia: {
    facebook: "",
    instagram: "",
    twitter: "",
  },

  // Payment methods
  paymentMethods: {
    cod: true,
    bankTransfer: true,
    creditCard: false,
  },

  // Shipping settings
  shippingSettings: {
    freeShippingThreshold: 500000,
    shippingFee: 30000,
    estimatedDelivery: "2-3 ngày",
  },

  // Email settings
  emailSettings: {
    smtpHost: "",
    smtpPort: 587,
    smtpUser: "",
    smtpPassword: "",
    fromEmail: "<EMAIL>",
    fromName: "NS Shop",
  },

  // Notification settings
  notifications: {
    orderNotifications: true,
    stockAlerts: true,
    customerNotifications: true,
  },

  // SEO settings
  seoSettings: {
    metaTitle: "NS Shop - Thời trang trực tuyến",
    metaDescription:
      "Cửa hàng thời trang trực tuyến với những sản phẩm chất lượng cao",
    metaKeywords: "thời trang, quần áo, giày dép, phụ kiện",
    googleAnalytics: "",
    facebookPixel: "",
  },

  // Security settings
  securitySettings: {
    enableTwoFactor: false,
    sessionTimeout: 24, // hours
    maxLoginAttempts: 5,
    enableCaptcha: false,
  },
};

// GET /api/admin/settings - Lấy tất cả settings
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Get all settings from database
    const settings = await prisma.setting.findMany();

    // Convert to key-value object
    const settingsObject: any = { ...DEFAULT_SETTINGS };

    settings.forEach((setting) => {
      settingsObject[setting.key] = setting.value;
    });

    return NextResponse.json(settingsObject);
  } catch (error) {
    console.error("Get settings error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy cài đặt" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/settings - Cập nhật settings
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { settings } = body;

    if (!settings || typeof settings !== "object") {
      return NextResponse.json(
        { error: "Dữ liệu cài đặt không hợp lệ" },
        { status: 400 }
      );
    }

    // Update each setting
    const updatePromises = Object.entries(settings).map(
      async ([key, value]) => {
        return prisma.setting.upsert({
          where: { key },
          update: {
            value: value as any,
            updatedAt: new Date(),
          },
          create: {
            key,
            value: value as any,
            type: typeof value === "object" ? "json" : typeof value,
          },
        });
      }
    );

    await Promise.all(updatePromises);

    return NextResponse.json({
      success: true,
      message: "Cài đặt đã được cập nhật thành công",
    });
  } catch (error) {
    console.error("Update settings error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi cập nhật cài đặt" },
      { status: 500 }
    );
  }
}

// POST /api/admin/settings/reset - Reset về cài đặt mặc định
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get("action");

    if (action === "reset") {
      // Delete all existing settings
      await prisma.setting.deleteMany();

      // Create default settings
      const createPromises = Object.entries(DEFAULT_SETTINGS).map(
        ([key, value]) => {
          return prisma.setting.create({
            data: {
              key,
              value: value as any,
              type: typeof value === "object" ? "json" : typeof value,
            },
          });
        }
      );

      await Promise.all(createPromises);

      return NextResponse.json({
        success: true,
        message: "Đã khôi phục cài đặt mặc định",
      });
    }

    return NextResponse.json({ error: "Action không hợp lệ" }, { status: 400 });
  } catch (error) {
    console.error("Reset settings error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi khôi phục cài đặt" },
      { status: 500 }
    );
  }
}
