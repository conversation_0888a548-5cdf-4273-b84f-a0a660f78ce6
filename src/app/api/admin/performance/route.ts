import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import {
  PerformanceOptimization,
  PerformanceMonitor,
  CacheManager,
  DatabaseOptimizer,
} from "@/lib/performance-optimization";

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const session = await getServerSession(adminAuthOptions);
    if (!session?.user || session.user.type !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Only ADMIN can access performance metrics
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Forbidden" },
        { status: 403 }
      );
    }

    const url = new URL(request.url);
    const action = url.searchParams.get("action");

    switch (action) {
      case "stats":
        return getPerformanceStats();
      case "cache":
        return getCacheStats();
      case "optimize":
        return runOptimization();
      default:
        return getOverview();
    }
  } catch (error) {
    console.error("Performance API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

async function getOverview() {
  const performanceStats = PerformanceMonitor.getStats();
  const cacheStats = CacheManager.getStats();
  
  // Get database stats
  const { prisma } = await import("@/lib/prisma");
  
  const [auditLogCount, notificationCount] = await Promise.all([
    prisma.auditLog.count(),
    prisma.notification.count(),
  ]);

  return NextResponse.json({
    overview: {
      auditLogs: auditLogCount,
      notifications: notificationCount,
      cacheEntries: cacheStats.totalEntries,
      performanceMetrics: Object.keys(performanceStats).length,
    },
    performance: performanceStats,
    cache: cacheStats,
    recommendations: getPerformanceRecommendations(performanceStats, cacheStats),
  });
}

async function getPerformanceStats() {
  const stats = PerformanceMonitor.getStats();
  
  return NextResponse.json({
    metrics: stats,
    summary: {
      totalOperations: Object.values(stats).reduce((sum: number, stat: any) => sum + stat.count, 0),
      averageResponseTime: calculateOverallAverage(stats),
      slowestOperation: findSlowestOperation(stats),
      fastestOperation: findFastestOperation(stats),
    },
  });
}

async function getCacheStats() {
  const stats = CacheManager.getStats();
  
  return NextResponse.json({
    cache: stats,
    hitRate: calculateCacheHitRate(),
    recommendations: getCacheRecommendations(stats),
  });
}

async function runOptimization() {
  try {
    const results = {
      archivedLogs: 0,
      cleanedNotifications: 0,
      cacheCleared: false,
      indexesOptimized: false,
    };

    // Archive old audit logs (older than 1 year)
    const archiveResult = await DatabaseOptimizer.archiveOldAuditLogs(365);
    results.archivedLogs = archiveResult.archived;

    // Clean up old notifications (older than 3 months)
    const cleanupResult = await DatabaseOptimizer.cleanupOldNotifications(90);
    results.cleanedNotifications = cleanupResult.cleaned;

    // Clear expired cache entries
    const cacheStats = CacheManager.getStats();
    if (cacheStats.expiredEntries > 0) {
      CacheManager.clear();
      results.cacheCleared = true;
    }

    // Optimize indexes (in a real app, this would run actual SQL)
    const indexResult = await DatabaseOptimizer.optimizeIndexes();
    results.indexesOptimized = indexResult.indexes.length > 0;

    return NextResponse.json({
      message: "Optimization completed successfully",
      results,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Optimization error:", error);
    return NextResponse.json(
      { error: "Optimization failed", details: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);
    if (!session?.user || session.user.type !== "admin" || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { action } = body;

    switch (action) {
      case "clear_cache":
        return clearCache(body.pattern);
      case "archive_logs":
        return archiveLogs(body.days);
      case "cleanup_notifications":
        return cleanupNotifications(body.days);
      default:
        return NextResponse.json({ error: "Invalid action" }, { status: 400 });
    }
  } catch (error) {
    console.error("Performance POST error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

async function clearCache(pattern?: string) {
  try {
    const statsBefore = CacheManager.getStats();
    CacheManager.clear(pattern);
    const statsAfter = CacheManager.getStats();

    return NextResponse.json({
      message: "Cache cleared successfully",
      before: statsBefore,
      after: statsAfter,
      cleared: statsBefore.totalEntries - statsAfter.totalEntries,
    });
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to clear cache" },
      { status: 500 }
    );
  }
}

async function archiveLogs(days: number = 365) {
  try {
    const result = await DatabaseOptimizer.archiveOldAuditLogs(days);
    return NextResponse.json({
      message: "Audit logs archived successfully",
      archived: result.archived,
      cutoffDays: days,
    });
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to archive logs" },
      { status: 500 }
    );
  }
}

async function cleanupNotifications(days: number = 90) {
  try {
    const result = await DatabaseOptimizer.cleanupOldNotifications(days);
    return NextResponse.json({
      message: "Notifications cleaned up successfully",
      cleaned: result.cleaned,
      cutoffDays: days,
    });
  } catch (error) {
    return NextResponse.json(
      { error: "Failed to cleanup notifications" },
      { status: 500 }
    );
  }
}

// Helper functions
function calculateOverallAverage(stats: any): number {
  const operations = Object.values(stats) as any[];
  if (operations.length === 0) return 0;

  const totalTime = operations.reduce((sum, op) => sum + (op.avg * op.count), 0);
  const totalCount = operations.reduce((sum, op) => sum + op.count, 0);

  return totalCount > 0 ? totalTime / totalCount : 0;
}

function findSlowestOperation(stats: any): { operation: string; avg: number } | null {
  let slowest = null;
  let maxAvg = 0;

  for (const [operation, stat] of Object.entries(stats) as [string, any][]) {
    if (stat.avg > maxAvg) {
      maxAvg = stat.avg;
      slowest = { operation, avg: stat.avg };
    }
  }

  return slowest;
}

function findFastestOperation(stats: any): { operation: string; avg: number } | null {
  let fastest = null;
  let minAvg = Infinity;

  for (const [operation, stat] of Object.entries(stats) as [string, any][]) {
    if (stat.avg < minAvg) {
      minAvg = stat.avg;
      fastest = { operation, avg: stat.avg };
    }
  }

  return fastest;
}

function calculateCacheHitRate(): number {
  // This would need to be tracked separately in a real implementation
  return 0.85; // Mock 85% hit rate
}

function getPerformanceRecommendations(performanceStats: any, cacheStats: any): string[] {
  const recommendations = [];

  // Check for slow operations
  for (const [operation, stat] of Object.entries(performanceStats) as [string, any][]) {
    if (stat.avg > 1000) { // Slower than 1 second
      recommendations.push(`Operation "${operation}" is slow (${stat.avg.toFixed(0)}ms avg). Consider optimization.`);
    }
  }

  // Check cache usage
  if (cacheStats.validEntries < 10) {
    recommendations.push("Low cache usage detected. Consider implementing more caching.");
  }

  if (cacheStats.expiredEntries > cacheStats.validEntries) {
    recommendations.push("Many expired cache entries. Consider adjusting TTL values.");
  }

  // Memory usage
  if (cacheStats.memoryUsage > 10 * 1024 * 1024) { // 10MB
    recommendations.push("High cache memory usage. Consider clearing old entries.");
  }

  if (recommendations.length === 0) {
    recommendations.push("System performance looks good!");
  }

  return recommendations;
}

function getCacheRecommendations(cacheStats: any): string[] {
  const recommendations = [];

  if (cacheStats.expiredEntries > 0) {
    recommendations.push(`Clear ${cacheStats.expiredEntries} expired cache entries`);
  }

  if (cacheStats.validEntries > 1000) {
    recommendations.push("Consider implementing cache size limits");
  }

  const hitRate = calculateCacheHitRate();
  if (hitRate < 0.7) {
    recommendations.push("Cache hit rate is low. Review caching strategy");
  }

  return recommendations;
}
