import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { verifyAdminToken } from "@/lib/admin-auth";

// Validation schema
const updateMenuItemSchema = z.object({
  parentId: z.string().optional(),
  title: z.string().min(1, "Tiêu đề là bắt buộc").optional(),
  url: z.string().optional(),
  type: z
    .enum(["LINK", "PAGE", "CATEGORY", "PRODUCT", "CUSTOM", "SEPARATOR"])
    .optional(),
  target: z.string().optional(),
  icon: z.string().optional(),
  cssClass: z.string().optional(),
  order: z.number().int().min(0).optional(),
  isActive: z.boolean().optional(),
});

// GET /api/admin/menu-items/[id] - Get single menu item
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const menuItem = await prisma.menuItem.findUnique({
      where: { id: params.id },
      include: {
        children: {
          orderBy: { order: "asc" },
        },
        parent: true,
        menu: true,
      },
    });

    if (!menuItem) {
      return NextResponse.json(
        { success: false, error: "Menu item không tồn tại" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: menuItem,
    });
  } catch (error) {
    console.error("Error fetching menu item:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy thông tin menu item" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/menu-items/[id] - Update menu item
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = updateMenuItemSchema.parse(body);

    // Check if menu item exists
    const existingMenuItem = await prisma.menuItem.findUnique({
      where: { id: params.id },
    });

    if (!existingMenuItem) {
      return NextResponse.json(
        { success: false, error: "Menu item không tồn tại" },
        { status: 404 }
      );
    }

    // Check if parent exists and is valid (if parentId provided)
    if (validatedData.parentId) {
      const parent = await prisma.menuItem.findUnique({
        where: { id: validatedData.parentId },
      });

      if (!parent || parent.menuId !== existingMenuItem.menuId) {
        return NextResponse.json(
          { success: false, error: "Menu item cha không hợp lệ" },
          { status: 400 }
        );
      }

      // Prevent circular reference
      if (validatedData.parentId === params.id) {
        return NextResponse.json(
          { success: false, error: "Không thể đặt chính nó làm menu item cha" },
          { status: 400 }
        );
      }
    }

    const updatedMenuItem = await prisma.menuItem.update({
      where: { id: params.id },
      data: validatedData,
      include: {
        children: {
          orderBy: { order: "asc" },
        },
        parent: true,
        menu: true,
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedMenuItem,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Error updating menu item:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi cập nhật menu item" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/menu-items/[id] - Delete menu item
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    // Check if menu item exists
    const menuItem = await prisma.menuItem.findUnique({
      where: { id: params.id },
      include: {
        children: true,
      },
    });

    if (!menuItem) {
      return NextResponse.json(
        { success: false, error: "Menu item không tồn tại" },
        { status: 404 }
      );
    }

    // Delete menu item (children will be deleted automatically due to cascade)
    await prisma.menuItem.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      success: true,
      message: "Đã xóa menu item thành công",
    });
  } catch (error) {
    console.error("Error deleting menu item:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi xóa menu item" },
      { status: 500 }
    );
  }
}
