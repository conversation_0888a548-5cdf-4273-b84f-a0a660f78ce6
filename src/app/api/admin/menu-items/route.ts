import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { verifyAdminToken } from "@/lib/admin-auth";

// Validation schemas
const createMenuItemSchema = z.object({
  menuId: z.string().min(1, "Menu ID là bắt buộc"),
  parentId: z.string().optional(),
  title: z.string().min(1, "Tiêu đề là bắt buộc"),
  url: z.string().optional(),
  type: z.enum(["LINK", "PAGE", "CATEGORY", "PRODUCT", "CUSTOM", "SEPARATOR"]),
  target: z.string().optional(),
  icon: z.string().optional(),
  cssClass: z.string().optional(),
  order: z.number().int().min(0).default(0),
  isActive: z.boolean().default(true),
});

const updateMenuItemSchema = createMenuItemSchema
  .partial()
  .omit({ menuId: true });

// GET /api/admin/menu-items - Get menu items for a specific menu
export async function GET(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const menuId = searchParams.get("menuId");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "50");
    const search = searchParams.get("search") || "";

    if (!menuId) {
      return NextResponse.json(
        { success: false, error: "Menu ID là bắt buộc" },
        { status: 400 }
      );
    }

    const skip = (page - 1) * limit;

    const where = {
      menuId,
      ...(search && {
        title: {
          contains: search,
          mode: "insensitive" as const,
        },
      }),
    };

    const [menuItems, total] = await Promise.all([
      prisma.menuItem.findMany({
        where,
        include: {
          children: {
            orderBy: { order: "asc" },
          },
          parent: true,
        },
        orderBy: { order: "asc" },
        skip,
        take: limit,
      }),
      prisma.menuItem.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      data: menuItems,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching menu items:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy danh sách menu items" },
      { status: 500 }
    );
  }
}

// POST /api/admin/menu-items - Create new menu item
export async function POST(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = createMenuItemSchema.parse(body);

    // Check if menu exists
    const menu = await prisma.menu.findUnique({
      where: { id: validatedData.menuId },
    });

    if (!menu) {
      return NextResponse.json(
        { success: false, error: "Menu không tồn tại" },
        { status: 404 }
      );
    }

    // Check if parent exists (if parentId provided)
    if (validatedData.parentId) {
      const parent = await prisma.menuItem.findUnique({
        where: { id: validatedData.parentId },
      });

      if (!parent || parent.menuId !== validatedData.menuId) {
        return NextResponse.json(
          { success: false, error: "Menu item cha không hợp lệ" },
          { status: 400 }
        );
      }
    }

    const menuItem = await prisma.menuItem.create({
      data: validatedData,
      include: {
        children: true,
        parent: true,
      },
    });

    return NextResponse.json({
      success: true,
      data: menuItem,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Error creating menu item:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi tạo menu item" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/menu-items/reorder - Reorder menu items
export async function PUT(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { items } = body;

    if (!items || !Array.isArray(items)) {
      return NextResponse.json(
        { success: false, error: "Danh sách items không hợp lệ" },
        { status: 400 }
      );
    }

    // Update order for each item
    const updatePromises = items.map((item: { id: string; order: number }) =>
      prisma.menuItem.update({
        where: { id: item.id },
        data: { order: item.order },
      })
    );

    await Promise.all(updatePromises);

    return NextResponse.json({
      success: true,
      message: "Đã cập nhật thứ tự menu items",
    });
  } catch (error) {
    console.error("Error reordering menu items:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi sắp xếp menu items" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/menu-items - Bulk delete menu items
export async function DELETE(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const idsParam = searchParams.get("ids");

    if (!idsParam) {
      return NextResponse.json(
        { success: false, error: "Danh sách ID không hợp lệ" },
        { status: 400 }
      );
    }

    const ids = idsParam.split(",");

    // Delete menu items (children will be deleted automatically due to cascade)
    await prisma.menuItem.deleteMany({
      where: {
        id: { in: ids },
      },
    });

    return NextResponse.json({
      success: true,
      message: `Đã xóa ${ids.length} menu item`,
    });
  } catch (error) {
    console.error("Error deleting menu items:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi xóa menu items" },
      { status: 500 }
    );
  }
}
