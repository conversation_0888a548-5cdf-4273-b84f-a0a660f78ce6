import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { jwtVerify } from "jose";

// Helper function to verify admin JWT token
async function verifyAdminToken(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;
    if (!adminToken) return null;

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );
    const { payload } = await jwtVerify(adminToken, secret);
    return payload;
  } catch {
    return null;
  }
}

// GET /api/admin/categories - <PERSON><PERSON>y danh sách categories cho admin
export async function GET(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);

    if (!adminToken || adminToken.type !== "admin") {
      return NextResponse.json(
        { success: false, error: "<PERSON>hông có quyền truy cập" },
        { status: 403 }
      );
    }

    // Both ADMIN and MODERATOR can manage categories
    if (adminToken.role !== "ADMIN" && adminToken.role !== "MODERATOR") {
      return NextResponse.json(
        { success: false, error: "Không có quyền quản lý danh mục" },
        { status: 403 }
      );
    }

    const categories = await prisma.category.findMany({
      include: {
        _count: {
          select: {
            products: true,
            children: true,
          },
        },
      },
      orderBy: [
        { parentId: "asc" }, // Parent categories first
        { name: "asc" },
      ],
    });

    return NextResponse.json({
      success: true,
      data: categories,
    });
  } catch (error) {
    console.error("Get admin categories error:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi lấy danh sách danh mục" },
      { status: 500 }
    );
  }
}

// POST /api/admin/categories - Tạo danh mục mới
export async function POST(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);

    if (!adminToken || adminToken.role !== "ADMIN") {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { name, description, slug, parentId } = body;

    // Validate required fields
    if (!name || !slug) {
      return NextResponse.json(
        { error: "Thiếu thông tin bắt buộc" },
        { status: 400 }
      );
    }

    // Check if slug already exists
    const existingSlug = await prisma.category.findUnique({
      where: { slug },
    });

    if (existingSlug) {
      return NextResponse.json({ error: "Slug đã tồn tại" }, { status: 400 });
    }

    // If parentId is provided, check if parent exists
    if (parentId) {
      const parentCategory = await prisma.category.findUnique({
        where: { id: parentId },
      });

      if (!parentCategory) {
        return NextResponse.json(
          { error: "Danh mục cha không tồn tại" },
          { status: 400 }
        );
      }
    }

    // Create category
    const category = await prisma.category.create({
      data: {
        name,
        description: description || null,
        slug,
        parentId: parentId || null,
      },
      include: {
        _count: {
          select: {
            products: true,
            children: true,
          },
        },
      },
    });

    return NextResponse.json(category, { status: 201 });
  } catch (error) {
    console.error("Create category error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tạo danh mục" },
      { status: 500 }
    );
  }
}
