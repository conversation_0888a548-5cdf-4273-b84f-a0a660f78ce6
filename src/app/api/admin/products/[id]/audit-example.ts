import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { prisma } from "@/lib/prisma";
import { withAudit, auditConfigs } from "@/lib/audit-middleware";
import {
  triggerLowStockAlert,
  triggerNewOrderNotification,
} from "@/lib/notification-rules";

interface RouteParams {
  params: {
    id: string;
  };
}

// Example of how to integrate audit logging into product operations

// GET with audit logging (for sensitive data access)
const auditedGET = withAudit("VIEW", "Product", {
  getResourceId: (request: NextRequest, params: any) => params?.id,
  getDescription: (request: NextRequest, params: any) =>
    `Viewed product details: ${params?.id}`,
  skipLogging: (request: NextRequest, params: any) => {
    // Skip logging for non-sensitive operations
    return false;
  },
})(async (request: NextRequest, context: any) => {
  const session = await getServerSession(adminAuthOptions);
  if (!session?.user || session.user.type !== "admin") {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { id } = context.params;

  const product = await prisma.product.findUnique({
    where: { id },
    include: {
      category: true,
      brand: true,
    },
  });

  if (!product) {
    return NextResponse.json({ error: "Product not found" }, { status: 404 });
  }

  return NextResponse.json({ data: product });
});

// PUT with audit logging (for updates)
const auditedPUT = withAudit(
  "UPDATE",
  "Product",
  auditConfigs.updateProduct
)(async (request: NextRequest, context: any) => {
  const session = await getServerSession(adminAuthOptions);
  if (!session?.user || session.user.type !== "admin") {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { id } = context.params;
  const body = await request.json();

  // Get old values for audit
  const oldProduct = await prisma.product.findUnique({
    where: { id },
  });

  if (!oldProduct) {
    return NextResponse.json({ error: "Product not found" }, { status: 404 });
  }

  // Update product
  const updatedProduct = await prisma.product.update({
    where: { id },
    data: body,
    include: {
      category: true,
      brand: true,
    },
  });

  // Check for low stock and trigger notification
  if (updatedProduct.stock <= 10 && oldProduct.stock > 10) {
    await triggerLowStockAlert(
      updatedProduct.id,
      updatedProduct.name,
      updatedProduct.stock
    );
  }

  return NextResponse.json({
    message: "Product updated successfully",
    data: updatedProduct,
  });
});

// DELETE with audit logging
const auditedDELETE = withAudit(
  "DELETE",
  "Product",
  auditConfigs.deleteProduct
)(async (request: NextRequest, context: any) => {
  const session = await getServerSession(adminAuthOptions);
  if (!session?.user || session.user.type !== "admin") {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Only ADMIN can delete products
  if (session.user.role !== "ADMIN") {
    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  }

  const { id } = context.params;

  // Check if product exists
  const product = await prisma.product.findUnique({
    where: { id },
  });

  if (!product) {
    return NextResponse.json({ error: "Product not found" }, { status: 404 });
  }

  // Check if product has orders
  const orderCount = await prisma.orderItem.count({
    where: { productId: id },
  });

  if (orderCount > 0) {
    return NextResponse.json(
      { error: "Cannot delete product with existing orders" },
      { status: 400 }
    );
  }

  // Delete product
  await prisma.product.delete({
    where: { id },
  });

  return NextResponse.json({
    message: "Product deleted successfully",
  });
});

// Export the audited handlers
export { auditedGET as GET, auditedPUT as PUT, auditedDELETE as DELETE };

// Example of manual audit logging for complex operations
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(adminAuthOptions);
    if (!session?.user || session.user.type !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;
    const body = await request.json();
    const { action } = body;

    // Get current product state
    const currentProduct = await prisma.product.findUnique({
      where: { id },
    });

    if (!currentProduct) {
      return NextResponse.json({ error: "Product not found" }, { status: 404 });
    }

    let result;
    let auditDescription = "";

    switch (action) {
      case "toggle_featured":
        result = await prisma.product.update({
          where: { id },
          data: { featured: !currentProduct.featured },
        });
        auditDescription = `Toggled featured status to ${result.featured}`;
        break;

      case "update_stock":
        const { stock } = body;
        const oldStock = currentProduct.stock;

        result = await prisma.product.update({
          where: { id },
          data: { stock: parseInt(stock) },
        });

        auditDescription = `Updated stock from ${oldStock} to ${stock}`;

        // Trigger low stock notification if needed
        if (stock <= 10 && oldStock > 10) {
          await triggerLowStockAlert(id, currentProduct.name, stock);
        }
        break;

      case "change_status":
        const { status } = body;
        result = await prisma.product.update({
          where: { id },
          data: { status },
        });
        auditDescription = `Changed status from ${currentProduct.status} to ${status}`;
        break;

      default:
        return NextResponse.json({ error: "Invalid action" }, { status: 400 });
    }

    // Manual audit logging for complex operations
    const { logAdminAction, getRequestMetadata } = await import(
      "@/lib/audit-logger"
    );
    const { ipAddress, userAgent } = getRequestMetadata(request);

    await logAdminAction({
      action: "UPDATE",
      resource: "Product",
      resourceId: id,
      oldValues: {
        [action]: currentProduct[action as keyof typeof currentProduct],
      },
      newValues: { [action]: result[action as keyof typeof result] },
      description: auditDescription,
      adminId: session.user.id,
      ipAddress,
      userAgent,
    });

    return NextResponse.json({
      message: "Product updated successfully",
      data: result,
    });
  } catch (error) {
    console.error("Product PATCH error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Example of batch operations with audit logging
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(adminAuthOptions);
    if (!session?.user || session.user.type !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { action, productIds } = body;

    if (!Array.isArray(productIds) || productIds.length === 0) {
      return NextResponse.json(
        { error: "Product IDs are required" },
        { status: 400 }
      );
    }

    const { logAdminAction, getRequestMetadata } = await import(
      "@/lib/audit-logger"
    );
    const { ipAddress, userAgent } = getRequestMetadata(request);

    let result;
    let auditDescription = "";

    switch (action) {
      case "bulk_delete":
        // Only ADMIN can bulk delete
        if (session.user.role !== "ADMIN") {
          return NextResponse.json({ error: "Forbidden" }, { status: 403 });
        }

        // Get products before deletion for audit
        const productsToDelete = await prisma.product.findMany({
          where: { id: { in: productIds } },
          select: { id: true, name: true, sku: true },
        });

        result = await prisma.product.deleteMany({
          where: { id: { in: productIds } },
        });

        auditDescription = `Bulk deleted ${result.count} products: ${productsToDelete.map((p) => p.name).join(", ")}`;

        // Log each deletion individually for better tracking
        for (const product of productsToDelete) {
          await logAdminAction({
            action: "DELETE",
            resource: "Product",
            resourceId: product.id,
            description: `Bulk deleted product: ${product.name} (${product.sku})`,
            adminId: session.user.id,
            ipAddress,
            userAgent,
          });
        }
        break;

      case "bulk_update_status":
        const { status } = body;

        result = await prisma.product.updateMany({
          where: { id: { in: productIds } },
          data: { status },
        });

        auditDescription = `Bulk updated status to ${status} for ${result.count} products`;

        await logAdminAction({
          action: "BULK_UPDATE",
          resource: "Product",
          description: auditDescription,
          newValues: { productIds, newStatus: status },
          adminId: session.user.id,
          ipAddress,
          userAgent,
        });
        break;

      default:
        return NextResponse.json({ error: "Invalid action" }, { status: 400 });
    }

    return NextResponse.json({
      message: "Bulk operation completed successfully",
      affected: result.count,
    });
  } catch (error) {
    console.error("Product bulk operation error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
