import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { verifyAdminToken } from "@/lib/admin-auth";
import { z } from "zod";

const createMenuSchema = z.object({
  name: z.string().min(1, "Tên menu không được để trống"),
  location: z.string().min(1, "Vị trí menu không được để trống"),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
});

const updateMenuSchema = z.object({
  name: z.string().min(1, "Tên menu không được để trống").optional(),
  location: z.string().min(1, "Vị trí menu không được để trống").optional(),
  description: z.string().optional(),
  isActive: z.boolean().optional(),
});

// GET /api/admin/menus - List all menus
export async function GET(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search") || "";
    const location = searchParams.get("location") || "";
    const isActive = searchParams.get("isActive");

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    if (location) {
      where.location = location;
    }

    if (isActive !== null && isActive !== undefined) {
      where.isActive = isActive === "true";
    }

    // Get menus with items count
    const [menus, total] = await Promise.all([
      prisma.menu.findMany({
        where,
        include: {
          items: {
            orderBy: { order: "asc" },
          },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.menu.count({ where }),
    ]);

    return NextResponse.json({
      success: true,
      data: menus,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching menus:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi tải danh sách menu" },
      { status: 500 }
    );
  }
}

// POST /api/admin/menus - Create new menu
export async function POST(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = createMenuSchema.parse(body);

    // Check if menu with same name and location already exists
    const existingMenu = await prisma.menu.findFirst({
      where: {
        name: validatedData.name,
        location: validatedData.location,
      },
    });

    if (existingMenu) {
      return NextResponse.json(
        { success: false, error: "Menu với tên và vị trí này đã tồn tại" },
        { status: 400 }
      );
    }

    const menu = await prisma.menu.create({
      data: validatedData,
      include: {
        items: {
          orderBy: { order: "asc" },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: menu,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Error creating menu:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi tạo menu" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/menus - Bulk update menus
export async function PUT(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { ids, data } = body;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { success: false, error: "Danh sách ID không hợp lệ" },
        { status: 400 }
      );
    }

    const validatedData = updateMenuSchema.parse(data);

    await prisma.menu.updateMany({
      where: {
        id: { in: ids },
      },
      data: validatedData,
    });

    return NextResponse.json({
      success: true,
      message: `Đã cập nhật ${ids.length} menu`,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Error bulk updating menus:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi cập nhật menu" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/menus - Bulk delete menus
export async function DELETE(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const idsParam = searchParams.get("ids");

    if (!idsParam) {
      return NextResponse.json(
        { success: false, error: "Danh sách ID không hợp lệ" },
        { status: 400 }
      );
    }

    const ids = idsParam.split(",");

    // Delete menus (menu items will be deleted automatically due to cascade)
    await prisma.menu.deleteMany({
      where: {
        id: { in: ids },
      },
    });

    return NextResponse.json({
      success: true,
      message: `Đã xóa ${ids.length} menu`,
    });
  } catch (error) {
    console.error("Error bulk deleting menus:", error);
    return NextResponse.json(
      { success: false, error: "Có lỗi xảy ra khi xóa menu" },
      { status: 500 }
    );
  }
}
