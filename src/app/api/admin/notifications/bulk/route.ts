import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { jwtVerify } from "jose";
import { z } from "zod";

// Helper function to verify admin authentication
async function verifyAdminAuth(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;

    if (!adminToken) {
      return null;
    }

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );

    const { payload } = await jwtVerify(adminToken, secret);

    if (payload.role !== "ADMIN" && payload.role !== "MODERATOR") {
      return null;
    }

    return {
      id: payload.id as string,
      role: payload.role as string,
      type: "admin" as const,
    };
  } catch {
    return null;
  }
}

const bulkActionSchema = z.object({
  action: z.enum(["mark_read", "mark_unread", "delete"]),
  notificationIds: z
    .array(z.string())
    .min(1, "<PERSON><PERSON><PERSON> chọn ít nhất một thông báo"),
});

// POST /api/admin/notifications/bulk - Bulk operations on notifications
export async function POST(request: NextRequest) {
  try {
    const adminUser = await verifyAdminAuth(request);

    if (!adminUser) {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { action, notificationIds } = bulkActionSchema.parse(body);

    // For delete action, only ADMIN can perform it
    if (action === "delete" && adminUser.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Chỉ Admin mới có quyền xóa thông báo" },
        { status: 403 }
      );
    }

    // Get notifications to verify access
    const notifications = await prisma.notification.findMany({
      where: {
        id: { in: notificationIds },
      },
    });

    if (notifications.length !== notificationIds.length) {
      return NextResponse.json(
        { error: "Một số thông báo không tồn tại" },
        { status: 400 }
      );
    }

    // Check access for each notification
    // For delete action, only ADMIN can delete any notification
    // For read/unread actions, users can only modify notifications they can see
    const accessibleNotifications = notifications.filter((notification) => {
      if (action === "delete" && adminUser.role === "ADMIN") {
        return true; // Admin can delete any notification
      }

      return (
        notification.targetType === "ALL_ADMINS" ||
        (notification.targetType === "SPECIFIC_ADMIN" &&
          notification.targetId === adminUser.id) ||
        notification.targetType === `ROLE_${adminUser.role}` ||
        adminUser.role === "ADMIN" // Admin can access all notifications
      );
    });

    // For read/unread actions, only process accessible notifications
    // For delete action, check if user has permission
    if (action === "delete" && adminUser.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Chỉ Admin mới có quyền xóa thông báo" },
        { status: 403 }
      );
    }

    // Use only accessible notifications for the operation
    const accessibleIds = accessibleNotifications.map((n) => n.id);

    let result;

    switch (action) {
      case "mark_read":
        result = await prisma.notification.updateMany({
          where: { id: { in: accessibleIds } },
          data: {
            isRead: true,
            readAt: new Date(),
          },
        });
        break;

      case "mark_unread":
        result = await prisma.notification.updateMany({
          where: { id: { in: accessibleIds } },
          data: {
            isRead: false,
            readAt: null,
          },
        });
        break;

      case "delete":
        result = await prisma.notification.deleteMany({
          where: { id: { in: accessibleIds } },
        });
        break;

      default:
        return NextResponse.json(
          { error: "Hành động không hợp lệ" },
          { status: 400 }
        );
    }

    const actionMessages = {
      mark_read: "Đánh dấu đã đọc",
      mark_unread: "Đánh dấu chưa đọc",
      delete: "Xóa",
    };

    return NextResponse.json({
      message: `${actionMessages[action]} ${result.count} thông báo thành công`,
      count: result.count,
      processedIds: accessibleIds,
      totalRequested: notificationIds.length,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Dữ liệu không hợp lệ", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Bulk notification operation error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi thực hiện thao tác" },
      { status: 500 }
    );
  }
}
