import { NextRequest } from "next/server";
import { jwtVerify } from "jose";
import { prisma } from "@/lib/prisma";

// Helper function to verify admin authentication
async function verifyAdminAuth(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;

    if (!adminToken) {
      return null;
    }

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );

    const { payload } = await jwtVerify(adminToken, secret);

    if (payload.role !== "ADMIN" && payload.role !== "MODERATOR") {
      return null;
    }

    return {
      id: payload.sub as string,
      role: payload.role as string,
      type: "admin" as const,
    };
  } catch {
    return null;
  }
}

// Server-Sent Events endpoint for real-time notifications
export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const adminUser = await verifyAdminAuth(request);
    if (!adminUser) {
      return new Response("Unauthorized", { status: 401 });
    }

    // Create a readable stream for SSE
    const stream = new ReadableStream({
      start(controller) {
        // Send initial connection message
        const encoder = new TextEncoder();
        const sendMessage = (data: any) => {
          const message = `data: ${JSON.stringify(data)}\n\n`;
          controller.enqueue(encoder.encode(message));
        };

        // Send connection established message
        sendMessage({
          type: "connection",
          message: "Connected to notification stream",
          timestamp: new Date().toISOString(),
        });

        // Function to fetch and send unread notifications
        const sendUnreadCount = async () => {
          try {
            const unreadCount = await prisma.notification.count({
              where: {
                AND: [
                  {
                    OR: [
                      { targetType: "ALL_ADMINS" },
                      { targetType: "SPECIFIC_ADMIN", targetId: adminUser.id },
                      {
                        targetType:
                          adminUser.role === "ADMIN"
                            ? "ROLE_ADMIN"
                            : "ROLE_MODERATOR",
                      },
                    ],
                  },
                  { isRead: false },
                  {
                    OR: [
                      { expiresAt: null },
                      { expiresAt: { gt: new Date() } },
                    ],
                  },
                ],
              },
            });

            sendMessage({
              type: "unread_count",
              count: unreadCount,
              timestamp: new Date().toISOString(),
            });
          } catch (error) {
            console.error("Error fetching unread count:", error);
          }
        };

        // Function to fetch and send latest notifications
        const sendLatestNotifications = async () => {
          try {
            const notifications = await prisma.notification.findMany({
              where: {
                AND: [
                  {
                    OR: [
                      { targetType: "ALL_ADMINS" },
                      { targetType: "SPECIFIC_ADMIN", targetId: adminUser.id },
                      {
                        targetType:
                          adminUser.role === "ADMIN"
                            ? "ROLE_ADMIN"
                            : "ROLE_MODERATOR",
                      },
                    ],
                  },
                  {
                    OR: [
                      { expiresAt: null },
                      { expiresAt: { gt: new Date() } },
                    ],
                  },
                ],
              },
              include: {
                creator: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                  },
                },
              },
              orderBy: [{ priority: "desc" }, { createdAt: "desc" }],
              take: 10, // Latest 10 notifications
            });

            sendMessage({
              type: "latest_notifications",
              notifications,
              timestamp: new Date().toISOString(),
            });
          } catch (error) {
            console.error("Error fetching latest notifications:", error);
          }
        };

        // Send initial data
        sendUnreadCount();
        sendLatestNotifications();

        // Set up polling for new notifications
        const pollInterval = setInterval(async () => {
          await sendUnreadCount();
          await sendLatestNotifications();
        }, 30000); // Poll every 30 seconds

        // Set up heartbeat to keep connection alive
        const heartbeatInterval = setInterval(() => {
          sendMessage({
            type: "heartbeat",
            timestamp: new Date().toISOString(),
          });
        }, 15000); // Heartbeat every 15 seconds

        // Cleanup function
        const cleanup = () => {
          clearInterval(pollInterval);
          clearInterval(heartbeatInterval);
        };

        // Handle client disconnect
        request.signal.addEventListener("abort", () => {
          cleanup();
          controller.close();
        });

        // Store cleanup function for potential future use
        (controller as any).cleanup = cleanup;
      },

      cancel() {
        // Cleanup when stream is cancelled
        if ((this as any).cleanup) {
          (this as any).cleanup();
        }
      },
    });

    // Return SSE response
    return new Response(stream, {
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Cache-Control",
      },
    });
  } catch (error) {
    console.error("SSE endpoint error:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

// Handle preflight requests for CORS
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization",
    },
  });
}
