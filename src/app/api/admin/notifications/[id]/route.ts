import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";

// GET /api/admin/notifications/[id] - Get specific notification
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.type !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const notification = await prisma.notification.findUnique({
      where: { id: params.id },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!notification) {
      return NextResponse.json(
        { error: "Không tìm thấy thông báo" },
        { status: 404 }
      );
    }

    // Check if user has access to this notification
    const hasAccess =
      notification.targetType === "ALL_ADMINS" ||
      (notification.targetType === "SPECIFIC_ADMIN" &&
        notification.targetId === session.user.id) ||
      notification.targetType === `ROLE_${session.user.role}` ||
      session.user.role === "ADMIN"; // Admin can see all notifications

    if (!hasAccess) {
      return NextResponse.json(
        { error: "Không có quyền xem thông báo này" },
        { status: 403 }
      );
    }

    return NextResponse.json(notification);
  } catch (error) {
    console.error("Get notification error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy thông báo" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/notifications/[id] - Update notification (mark as read)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.type !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { isRead } = body;

    const notification = await prisma.notification.findUnique({
      where: { id: params.id },
    });

    if (!notification) {
      return NextResponse.json(
        { error: "Không tìm thấy thông báo" },
        { status: 404 }
      );
    }

    // Check if user has access to this notification
    const hasAccess =
      notification.targetType === "ALL_ADMINS" ||
      (notification.targetType === "SPECIFIC_ADMIN" &&
        notification.targetId === session.user.id) ||
      notification.targetType === `ROLE_${session.user.role}` ||
      session.user.role === "ADMIN"; // Admin can update all notifications

    if (!hasAccess) {
      return NextResponse.json(
        { error: "Không có quyền cập nhật thông báo này" },
        { status: 403 }
      );
    }

    const updatedNotification = await prisma.notification.update({
      where: { id: params.id },
      data: {
        isRead: isRead,
        readAt: isRead ? new Date() : null,
      },
    });

    return NextResponse.json({
      message: "Cập nhật thông báo thành công",
      notification: updatedNotification,
    });
  } catch (error) {
    console.error("Update notification error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi cập nhật thông báo" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/notifications/[id] - Delete notification
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.type !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Only ADMIN can delete notifications
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Chỉ Admin mới có quyền xóa thông báo" },
        { status: 403 }
      );
    }

    const notification = await prisma.notification.findUnique({
      where: { id: params.id },
    });

    if (!notification) {
      return NextResponse.json(
        { error: "Không tìm thấy thông báo" },
        { status: 404 }
      );
    }

    await prisma.notification.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      message: "Xóa thông báo thành công",
    });
  } catch (error) {
    console.error("Delete notification error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi xóa thông báo" },
      { status: 500 }
    );
  }
}
