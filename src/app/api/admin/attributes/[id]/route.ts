import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// Validation schema
const updateAttributeSchema = z.object({
  name: z.string().min(1, "Tên thuộc tính là bắt buộc").optional(),
  slug: z.string().optional(),
  description: z.string().optional(),
  type: z
    .enum([
      "TEXT",
      "NUMBER",
      "COLOR",
      "SIZE",
      "BOOLEAN",
      "SELECT",
      "MULTI_SELECT",
    ])
    .optional(),
  isRequired: z.boolean().optional(),
  isFilterable: z.boolean().optional(),
  sortOrder: z.number().optional(),
  values: z
    .array(
      z.object({
        id: z.string().optional(),
        value: z.string().min(1),
        slug: z.string().optional(),
        sortOrder: z.number().default(0),
      })
    )
    .optional(),
});

// GET /api/admin/attributes/[id] - Lấy chi tiết attribute
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const attribute = await prisma.attribute.findUnique({
      where: { id: params.id },
      include: {
        values: {
          orderBy: { sortOrder: "asc" },
        },
        _count: {
          select: {
            values: true,
            products: true,
          },
        },
      },
    });

    if (!attribute) {
      return NextResponse.json(
        { error: "Không tìm thấy thuộc tính" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: attribute,
    });
  } catch (error) {
    console.error("Get attribute error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy thông tin thuộc tính" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/attributes/[id] - Cập nhật attribute
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = updateAttributeSchema.parse(body);

    // Check if attribute exists
    const existingAttribute = await prisma.attribute.findUnique({
      where: { id: params.id },
      include: { values: true },
    });

    if (!existingAttribute) {
      return NextResponse.json(
        { error: "Không tìm thấy thuộc tính" },
        { status: 404 }
      );
    }

    // Generate slug if name is updated
    if (validatedData.name && !validatedData.slug) {
      validatedData.slug = validatedData.name
        .toLowerCase()
        .replace(/\s+/g, "-")
        .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
        .replace(/[èéẹẻẽêềếệểễ]/g, "e")
        .replace(/[ìíịỉĩ]/g, "i")
        .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
        .replace(/[ùúụủũưừứựửữ]/g, "u")
        .replace(/[ỳýỵỷỹ]/g, "y")
        .replace(/[đ]/g, "d")
        .replace(/[^a-z0-9-]/g, "");
    }

    // Check if slug already exists (if changed)
    if (validatedData.slug && validatedData.slug !== existingAttribute.slug) {
      const slugExists = await prisma.attribute.findUnique({
        where: { slug: validatedData.slug },
      });

      if (slugExists) {
        return NextResponse.json({ error: "Slug đã tồn tại" }, { status: 400 });
      }
    }

    // Update attribute with values in transaction
    const updatedAttribute = await prisma.$transaction(async (tx) => {
      // Update attribute
      const attribute = await tx.attribute.update({
        where: { id: params.id },
        data: {
          ...(validatedData.name && { name: validatedData.name }),
          ...(validatedData.slug && { slug: validatedData.slug }),
          ...(validatedData.description !== undefined && {
            description: validatedData.description,
          }),
          ...(validatedData.type && { type: validatedData.type }),
          ...(validatedData.isRequired !== undefined && {
            isRequired: validatedData.isRequired,
          }),
          ...(validatedData.isFilterable !== undefined && {
            isFilterable: validatedData.isFilterable,
          }),
          ...(validatedData.sortOrder !== undefined && {
            sortOrder: validatedData.sortOrder,
          }),
        },
      });

      // Update values if provided
      if (validatedData.values) {
        // Delete existing values that are not in the new list
        const newValueIds = validatedData.values
          .filter((v) => v.id)
          .map((v) => v.id!);

        await tx.attributeValue.deleteMany({
          where: {
            attributeId: params.id,
            id: { notIn: newValueIds },
          },
        });

        // Update or create values
        for (const valueData of validatedData.values) {
          const valueSlug =
            valueData.slug ||
            valueData.value
              .toLowerCase()
              .replace(/\s+/g, "-")
              .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
              .replace(/[èéẹẻẽêềếệểễ]/g, "e")
              .replace(/[ìíịỉĩ]/g, "i")
              .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
              .replace(/[ùúụủũưừứựửữ]/g, "u")
              .replace(/[ỳýỵỷỹ]/g, "y")
              .replace(/[đ]/g, "d")
              .replace(/[^a-z0-9-]/g, "");

          if (valueData.id) {
            // Update existing value
            await tx.attributeValue.update({
              where: { id: valueData.id },
              data: {
                value: valueData.value,
                slug: valueSlug,
                sortOrder: valueData.sortOrder,
              },
            });
          } else {
            // Create new value
            await tx.attributeValue.create({
              data: {
                attributeId: params.id,
                value: valueData.value,
                slug: valueSlug,
                sortOrder: valueData.sortOrder,
              },
            });
          }
        }
      }

      return attribute;
    });

    // Fetch the updated attribute with values
    const result = await prisma.attribute.findUnique({
      where: { id: updatedAttribute.id },
      include: {
        values: {
          orderBy: { sortOrder: "asc" },
        },
        _count: {
          select: {
            values: true,
            products: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: result,
      message: "Thuộc tính đã được cập nhật thành công",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Dữ liệu không hợp lệ", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Update attribute error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi cập nhật thuộc tính" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/attributes/[id] - Xóa attribute
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    // Check if attribute exists
    const existingAttribute = await prisma.attribute.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: { products: true },
        },
      },
    });

    if (!existingAttribute) {
      return NextResponse.json(
        { error: "Không tìm thấy thuộc tính" },
        { status: 404 }
      );
    }

    // Check if attribute is being used by products
    if (existingAttribute._count.products > 0) {
      return NextResponse.json(
        {
          error: `Không thể xóa thuộc tính đang được sử dụng bởi ${existingAttribute._count.products} sản phẩm`,
        },
        { status: 400 }
      );
    }

    // Delete attribute (values will be deleted automatically due to cascade)
    await prisma.attribute.delete({
      where: { id: params.id },
    });

    return NextResponse.json({
      success: true,
      message: "Thuộc tính đã được xóa thành công",
    });
  } catch (error) {
    console.error("Delete attribute error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi xóa thuộc tính" },
      { status: 500 }
    );
  }
}
