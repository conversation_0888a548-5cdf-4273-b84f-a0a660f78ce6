import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// Validation schema for updating attribute value
const updateAttributeValueSchema = z.object({
  value: z.string().min(1, "Giá trị là bắt buộc").optional(),
  slug: z.string().optional(),
  sortOrder: z.number().optional(),
});

// GET /api/admin/attributes/[id]/values/[valueId] - L<PERSON>y thông tin chi tiết value
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string; valueId: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "<PERSON>hông có quyền truy cập" },
        { status: 401 }
      );
    }

    const attributeValue = await prisma.attributeValue.findFirst({
      where: {
        id: params.valueId,
        attributeId: params.id,
      },
      include: {
        attribute: true,
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    if (!attributeValue) {
      return NextResponse.json(
        { error: "Không tìm thấy giá trị thuộc tính" },
        { status: 404 }
      );
    }

    return NextResponse.json(attributeValue);
  } catch (error) {
    console.error("Error fetching attribute value:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tải thông tin giá trị" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/attributes/[id]/values/[valueId] - Cập nhật value
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string; valueId: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = updateAttributeValueSchema.parse(body);

    // Check if attribute value exists
    const existingValue = await prisma.attributeValue.findFirst({
      where: {
        id: params.valueId,
        attributeId: params.id,
      },
    });

    if (!existingValue) {
      return NextResponse.json(
        { error: "Không tìm thấy giá trị thuộc tính" },
        { status: 404 }
      );
    }

    // Auto-generate slug if value is updated but slug is not provided
    if (validatedData.value && !validatedData.slug) {
      validatedData.slug = validatedData.value
        .toLowerCase()
        .replace(/\s+/g, "-")
        .replace(/[àáạảãâầấậẩẫăằắặẳẵ]/g, "a")
        .replace(/[èéẹẻẽêềếệểễ]/g, "e")
        .replace(/[ìíịỉĩ]/g, "i")
        .replace(/[òóọỏõôồốộổỗơờớợởỡ]/g, "o")
        .replace(/[ùúụủũưừứựửữ]/g, "u")
        .replace(/[ỳýỵỷỹ]/g, "y")
        .replace(/[đ]/g, "d")
        .replace(/[^a-z0-9-]/g, "");
    }

    // Check for duplicate value or slug (excluding current value)
    if (validatedData.value || validatedData.slug) {
      const duplicateCheck: any = {
        attributeId: params.id,
        id: { not: params.valueId },
      };

      const orConditions = [];
      if (validatedData.value) {
        orConditions.push({ value: validatedData.value });
      }
      if (validatedData.slug) {
        orConditions.push({ slug: validatedData.slug });
      }

      if (orConditions.length > 0) {
        duplicateCheck.OR = orConditions;

        const duplicateValue = await prisma.attributeValue.findFirst({
          where: duplicateCheck,
        });

        if (duplicateValue) {
          return NextResponse.json(
            { error: "Giá trị hoặc slug đã tồn tại" },
            { status: 400 }
          );
        }
      }
    }

    // Update attribute value
    const updatedValue = await prisma.attributeValue.update({
      where: { id: params.valueId },
      data: validatedData,
      include: {
        attribute: true,
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    return NextResponse.json(updatedValue);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Dữ liệu không hợp lệ", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error updating attribute value:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi cập nhật giá trị" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/attributes/[id]/values/[valueId] - Xóa value
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string; valueId: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    // Check if attribute value exists
    const existingValue = await prisma.attributeValue.findFirst({
      where: {
        id: params.valueId,
        attributeId: params.id,
      },
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    if (!existingValue) {
      return NextResponse.json(
        { error: "Không tìm thấy giá trị thuộc tính" },
        { status: 404 }
      );
    }

    // Check if value is being used by products
    if (existingValue._count.products > 0) {
      return NextResponse.json(
        {
          error: `Không thể xóa giá trị này vì đang được sử dụng bởi ${existingValue._count.products} sản phẩm`,
        },
        { status: 400 }
      );
    }

    // Delete attribute value
    await prisma.attributeValue.delete({
      where: { id: params.valueId },
    });

    return NextResponse.json({ message: "Xóa giá trị thành công" });
  } catch (error) {
    console.error("Error deleting attribute value:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi xóa giá trị" },
      { status: 500 }
    );
  }
}
