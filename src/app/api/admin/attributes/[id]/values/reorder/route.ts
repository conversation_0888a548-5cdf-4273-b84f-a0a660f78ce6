import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { adminAuthOptions } from "@/lib/admin-auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// Validation schema for reordering values
const reorderValuesSchema = z.object({
  values: z.array(
    z.object({
      id: z.string(),
      sortOrder: z.number(),
    })
  ),
});

// PUT /api/admin/attributes/[id]/values/reorder - Sắp xếp lại thứ tự values
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(adminAuthOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const validatedData = reorderValuesSchema.parse(body);

    // Check if attribute exists
    const attribute = await prisma.attribute.findUnique({
      where: { id: params.id },
    });

    if (!attribute) {
      return NextResponse.json(
        { error: "Không tìm thấy thuộc tính" },
        { status: 404 }
      );
    }

    // Verify all values belong to this attribute
    const valueIds = validatedData.values.map((v) => v.id);
    const existingValues = await prisma.attributeValue.findMany({
      where: {
        id: { in: valueIds },
        attributeId: params.id,
      },
    });

    if (existingValues.length !== valueIds.length) {
      return NextResponse.json(
        { error: "Một số giá trị không thuộc về thuộc tính này" },
        { status: 400 }
      );
    }

    // Update sort orders in transaction
    await prisma.$transaction(
      validatedData.values.map((value) =>
        prisma.attributeValue.update({
          where: { id: value.id },
          data: { sortOrder: value.sortOrder },
        })
      )
    );

    // Return updated values
    const updatedValues = await prisma.attributeValue.findMany({
      where: {
        attributeId: params.id,
      },
      include: {
        _count: {
          select: {
            products: true,
          },
        },
      },
      orderBy: {
        sortOrder: "asc",
      },
    });

    return NextResponse.json({
      message: "Cập nhật thứ tự thành công",
      data: updatedValues,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Dữ liệu không hợp lệ", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error reordering attribute values:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi sắp xếp lại thứ tự" },
      { status: 500 }
    );
  }
}
