import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { verifyAdminToken } from "@/lib/admin-auth";

export async function GET(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);
    if (!adminToken) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const range = searchParams.get("range") || "30d";

    // Calculate date ranges
    const now = new Date();
    const getDaysAgo = (days: number) => {
      const date = new Date(now);
      date.setDate(date.getDate() - days);
      return date;
    };

    let startDate: Date;
    let previousStartDate: Date;

    switch (range) {
      case "7d":
        startDate = getDaysAgo(7);
        previousStartDate = getDaysAgo(14);
        break;
      case "90d":
        startDate = getDaysAgo(90);
        previousStartDate = getDaysAgo(180);
        break;
      case "1y":
        startDate = getDaysAgo(365);
        previousStartDate = getDaysAgo(730);
        break;
      default: // 30d
        startDate = getDaysAgo(30);
        previousStartDate = getDaysAgo(60);
        break;
    }

    // Get current period data
    const [
      totalRevenue,
      totalOrders,
      totalCustomers,
      totalProducts,
      previousRevenue,
      previousOrders,
      previousCustomers,
      previousProducts,
    ] = await Promise.all([
      // Current period revenue
      prisma.order.aggregate({
        where: {
          createdAt: { gte: startDate },
          status: { not: "CANCELLED" },
        },
        _sum: { total: true },
      }),

      // Current period orders
      prisma.order.count({
        where: {
          createdAt: { gte: startDate },
          status: { not: "CANCELLED" },
        },
      }),

      // Current period customers
      prisma.user.count({
        where: {
          createdAt: { gte: startDate },
        },
      }),

      // Total products
      prisma.product.count({
        where: {
          status: "ACTIVE",
        },
      }),

      // Previous period revenue
      prisma.order.aggregate({
        where: {
          createdAt: { gte: previousStartDate, lt: startDate },
          status: { not: "CANCELLED" },
        },
        _sum: { total: true },
      }),

      // Previous period orders
      prisma.order.count({
        where: {
          createdAt: { gte: previousStartDate, lt: startDate },
          status: { not: "CANCELLED" },
        },
      }),

      // Previous period customers
      prisma.user.count({
        where: {
          createdAt: { gte: previousStartDate, lt: startDate },
        },
      }),

      // Previous period products (for growth calculation)
      prisma.product.count({
        where: {
          createdAt: { lt: startDate },
          status: "ACTIVE",
        },
      }),
    ]);

    // Calculate growth percentages
    const calculateGrowth = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    };

    const currentRevenue = totalRevenue._sum.total || 0;
    const prevRevenue = previousRevenue._sum.total || 0;
    const currentProducts = totalProducts;
    const prevProducts = previousProducts;

    // Get monthly revenue data for charts
    const monthlyData = await prisma.order.groupBy({
      by: ["createdAt"],
      where: {
        createdAt: { gte: getDaysAgo(365) },
        status: { not: "CANCELLED" },
      },
      _sum: { total: true },
      _count: { id: true },
    });

    // Process monthly data
    const monthlyRevenue = Array.from({ length: 12 }, (_, i) => {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthStart = new Date(date.getFullYear(), date.getMonth(), 1);
      const monthEnd = new Date(date.getFullYear(), date.getMonth() + 1, 0);

      const monthData = monthlyData.filter((item) => {
        const itemDate = new Date(item.createdAt);
        return itemDate >= monthStart && itemDate <= monthEnd;
      });

      return {
        month: date.toLocaleDateString("vi-VN", {
          month: "short",
          year: "numeric",
        }),
        revenue: monthData.reduce(
          (sum, item) => sum + (item._sum.total || 0),
          0
        ),
        orders: monthData.reduce((sum, item) => sum + item._count.id, 0),
      };
    }).reverse();

    // Get top products
    const topProducts = await prisma.orderItem.groupBy({
      by: ["productId"],
      where: {
        order: {
          createdAt: { gte: startDate },
          status: { not: "CANCELLED" },
        },
      },
      _sum: {
        quantity: true,
        price: true,
      },
      orderBy: {
        _sum: {
          price: "desc",
        },
      },
      take: 5,
    });

    const topProductsWithDetails = await Promise.all(
      topProducts.map(async (item) => {
        const product = await prisma.product.findUnique({
          where: { id: item.productId },
          select: { id: true, name: true },
        });
        return {
          id: item.productId,
          name: product?.name || "Unknown Product",
          totalSold: item._sum.quantity || 0,
          revenue: item._sum.price || 0,
        };
      })
    );

    // Get top categories
    const topCategories = await prisma.orderItem.groupBy({
      by: ["productId"],
      where: {
        order: {
          createdAt: { gte: startDate },
          status: { not: "CANCELLED" },
        },
      },
      _sum: {
        price: true,
      },
    });

    const categoryRevenue = new Map<
      string,
      { revenue: number; productCount: number; name: string }
    >();

    for (const item of topCategories) {
      const product = await prisma.product.findUnique({
        where: { id: item.productId },
        include: { category: true },
      });

      if (product?.category) {
        const existing = categoryRevenue.get(product.category.id) || {
          revenue: 0,
          productCount: 0,
          name: product.category.name,
        };

        existing.revenue += item._sum.price || 0;
        existing.productCount += 1;
        categoryRevenue.set(product.category.id, existing);
      }
    }

    const topCategoriesArray = Array.from(categoryRevenue.entries())
      .map(([id, data]) => ({
        id,
        name: data.name,
        productCount: data.productCount,
        revenue: data.revenue,
      }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);

    // Get order status distribution
    const ordersByStatus = await prisma.order.groupBy({
      by: ["status"],
      where: {
        createdAt: { gte: startDate },
      },
      _count: { id: true },
    });

    const totalOrdersForStatus = ordersByStatus.reduce(
      (sum, item) => sum + item._count.id,
      0
    );
    const orderStatusData = ordersByStatus.map((item) => ({
      status: item.status,
      count: item._count.id,
      percentage:
        totalOrdersForStatus > 0
          ? (item._count.id / totalOrdersForStatus) * 100
          : 0,
    }));

    // Get recent activity (simplified)
    const recentActivity = await prisma.auditLog.findMany({
      where: {
        createdAt: { gte: getDaysAgo(7) },
      },
      include: {
        admin: {
          select: { name: true },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 10,
    });

    const activityData = recentActivity.map((log) => ({
      id: log.id,
      action: log.action,
      description: log.description || `${log.action} on ${log.resource}`,
      timestamp: log.createdAt.toLocaleDateString("vi-VN", {
        hour: "2-digit",
        minute: "2-digit",
        day: "2-digit",
        month: "2-digit",
      }),
      type: log.resource.toLowerCase().includes("order")
        ? "order"
        : log.resource.toLowerCase().includes("product")
          ? "product"
          : log.resource.toLowerCase().includes("user")
            ? "user"
            : "system",
    }));

    const dashboardData = {
      overview: {
        totalRevenue: currentRevenue,
        totalOrders,
        totalCustomers,
        totalProducts: currentProducts,
        revenueGrowth: calculateGrowth(currentRevenue, prevRevenue),
        ordersGrowth: calculateGrowth(totalOrders, previousOrders),
        customersGrowth: calculateGrowth(totalCustomers, previousCustomers),
        productsGrowth: calculateGrowth(currentProducts, prevProducts),
      },
      monthlyRevenue,
      topProducts: topProductsWithDetails,
      topCategories: topCategoriesArray,
      ordersByStatus: orderStatusData,
      recentActivity: activityData,
    };

    return NextResponse.json(dashboardData);
  } catch (error) {
    console.error("Dashboard overview error:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
