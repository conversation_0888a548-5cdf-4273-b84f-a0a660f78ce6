import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { jwtVerify } from "jose";

// Helper function to verify admin JWT token
async function verifyAdminToken(request: NextRequest) {
  try {
    const adminToken = request.cookies.get("admin-session")?.value;
    if (!adminToken) return null;

    const secret = new TextEncoder().encode(
      process.env.NEXTAUTH_SECRET || "fallback-secret"
    );
    const { payload } = await jwtVerify(adminToken, secret);
    return payload;
  } catch {
    return null;
  }
}

// GET /api/admin/dashboard - Lấy thống kê dashboard
export async function GET(request: NextRequest) {
  try {
    const adminToken = await verifyAdminToken(request);

    if (!adminToken || adminToken.type !== "admin") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    // Both ADMIN and MODERATOR can access dashboard
    if (adminToken.role !== "ADMIN" && adminToken.role !== "MODERATOR") {
      return NextResponse.json(
        { error: "Không có quyền truy cập dashboard" },
        { status: 403 }
      );
    }

    // Get current month and last month dates
    const now = new Date();
    const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

    // Get basic counts
    const [totalProducts, totalOrders, totalCustomers] = await Promise.all([
      prisma.product.count({
        where: { status: "ACTIVE" },
      }),
      prisma.order.count(),
      prisma.user.count(), // All users in User model are customers
    ]);

    // Get monthly stats
    const [currentMonthOrders, lastMonthOrders] = await Promise.all([
      prisma.order.findMany({
        where: {
          createdAt: {
            gte: currentMonthStart,
          },
        },
        select: {
          total: true,
        },
      }),
      prisma.order.findMany({
        where: {
          createdAt: {
            gte: lastMonthStart,
            lte: lastMonthEnd,
          },
        },
        select: {
          total: true,
        },
      }),
    ]);

    const currentMonthRevenue = currentMonthOrders.reduce(
      (sum, order) => sum + order.total,
      0
    );
    const lastMonthRevenue = lastMonthOrders.reduce(
      (sum, order) => sum + order.total,
      0
    );

    // Get recent orders
    const recentOrders = await prisma.order.findMany({
      take: 5,
      orderBy: {
        createdAt: "desc",
      },
      include: {
        user: {
          select: {
            name: true,
          },
        },
      },
    });

    // Get top products (by total sold quantity)
    const topProductsData = await prisma.orderItem.groupBy({
      by: ["productId"],
      _sum: {
        quantity: true,
        total: true,
      },
      orderBy: {
        _sum: {
          quantity: "desc",
        },
      },
      take: 5,
    });

    // Get product details for top products
    const topProducts = await Promise.all(
      topProductsData.map(async (item) => {
        const product = await prisma.product.findUnique({
          where: { id: item.productId },
          select: {
            id: true,
            name: true,
            slug: true,
          },
        });

        return {
          ...product,
          totalSold: item._sum.quantity || 0,
          revenue: item._sum.total || 0,
        };
      })
    );

    // Calculate total revenue
    const totalRevenue = await prisma.order.aggregate({
      _sum: {
        total: true,
      },
    });

    const dashboardStats = {
      totalProducts,
      totalOrders,
      totalCustomers,
      totalRevenue: totalRevenue._sum.total || 0,
      recentOrders,
      topProducts: topProducts.filter((p) => p.id), // Filter out null products
      monthlyStats: {
        currentMonth: {
          orders: currentMonthOrders.length,
          revenue: currentMonthRevenue,
        },
        lastMonth: {
          orders: lastMonthOrders.length,
          revenue: lastMonthRevenue,
        },
      },
    };

    return NextResponse.json(dashboardStats);
  } catch (error) {
    console.error("Get dashboard stats error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy thống kê dashboard" },
      { status: 500 }
    );
  }
}
