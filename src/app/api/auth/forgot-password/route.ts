import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { emailService } from "@/lib/email-service";
import type { PasswordResetEmailData } from "@/lib/email/templates";
import crypto from "crypto";

const forgotPasswordSchema = z.object({
  email: z.string().email("Email không hợp lệ"),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email } = forgotPasswordSchema.parse(body);

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { email },
      select: { id: true, name: true, email: true },
    });

    // Always return success to prevent email enumeration attacks
    const successResponse = NextResponse.json(
      {
        message:
          "Nếu email tồn tại trong hệ thống, bạn sẽ nhận được link đặt lại mật khẩu.",
        success: true,
      },
      { status: 200 }
    );

    if (!user) {
      // Still return success but don't send email
      return successResponse;
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString("hex");
    const resetTokenExpiry = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes

    // Save reset token to database
    await prisma.passwordResetToken.upsert({
      where: { userId: user.id },
      update: {
        token: resetToken,
        expiresAt: resetTokenExpiry,
        used: false,
      },
      create: {
        userId: user.id,
        token: resetToken,
        expiresAt: resetTokenExpiry,
        used: false,
      },
    });

    // Initialize email service
    const initialized = await emailService.initialize();
    if (!initialized) {
      console.error("Email service not initialized for password reset");
      return successResponse; // Still return success to user
    }

    // Prepare password reset email data
    const resetUrl = `${process.env.NEXT_PUBLIC_APP_URL}/auth/reset-password?token=${resetToken}`;
    const passwordResetData: PasswordResetEmailData = {
      recipientName: user.name,
      recipientEmail: user.email,
      resetUrl,
      expiresAt: resetTokenExpiry.toISOString(),
    };

    // Send password reset email
    try {
      await emailService.sendPasswordResetEmail(passwordResetData);
      console.log(`Password reset email sent to ${user.email}`);
    } catch (error) {
      console.error("Failed to send password reset email:", error);
      // Still return success to user
    }

    return successResponse;
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Forgot password error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi xử lý yêu cầu" },
      { status: 500 }
    );
  }
}
