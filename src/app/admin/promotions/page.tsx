"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Tag,
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  Plus,
  Copy,
  Calendar,
  Percent,
  DollarSign,
  Users,
  TrendingUp,
} from "lucide-react";
import { toast } from "sonner";
import { AdminDataTable } from "@/lib/admin/components/AdminDataTable";
import { useAdminData, useAdminCrud } from "@/lib/admin/hooks";
import { formatDistanceToNow } from "date-fns";
import { vi } from "date-fns/locale";

interface Promotion {
  id: string;
  name: string;
  description?: string;
  code: string;
  type: "PERCENTAGE" | "FIXED_AMOUNT" | "FREE_SHIPPING" | "BUY_X_GET_Y";
  value: number;
  minOrderAmount?: number;
  maxDiscountAmount?: number;
  usageLimit?: number;
  usageCount: number;
  userUsageLimit?: number;
  startDate: string;
  endDate?: string;
  isActive: boolean;
  applicableProducts: string[];
  applicableCategories: string[];
  excludedProducts: string[];
  excludedCategories: string[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

const promotionTypeLabels = {
  PERCENTAGE: "Giảm theo %",
  FIXED_AMOUNT: "Giảm cố định",
  FREE_SHIPPING: "Miễn phí ship",
  BUY_X_GET_Y: "Mua X tặng Y",
};

const promotionTypeColors = {
  PERCENTAGE: "bg-blue-100 text-blue-800",
  FIXED_AMOUNT: "bg-green-100 text-green-800",
  FREE_SHIPPING: "bg-purple-100 text-purple-800",
  BUY_X_GET_Y: "bg-orange-100 text-orange-800",
};

export default function AdminPromotionsPage() {
  const [selectedPromotion, setSelectedPromotion] = useState<Promotion | null>(
    null
  );
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [filterType, setFilterType] = useState<string>("");
  const [filterStatus, setFilterStatus] = useState<string>("");
  const [searchQuery, setSearchQuery] = useState<string>("");

  // Form state for create/edit
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    code: "",
    type: "PERCENTAGE" as const,
    value: 0,
    minOrderAmount: "",
    maxDiscountAmount: "",
    usageLimit: "",
    userUsageLimit: "",
    startDate: "",
    endDate: "",
    isActive: true,
  });

  // Data fetching
  const {
    data: promotions,
    loading,
    pagination,
    refresh,
    setParams,
  } = useAdminData<Promotion>({
    endpoint: "/api/admin/promotions",
    initialParams: { page: 1, limit: 20 },
  });

  // CRUD operations
  const {
    create: createPromotion,
    update: updatePromotion,
    delete: deletePromotion,
  } = useAdminCrud("/api/admin/promotions");

  // Handle search
  const handleSearch = (value: string) => {
    setSearchQuery(value);
    setParams({ search: value, page: 1 });
  };

  // Handle filter type
  const handleFilterType = (value: string) => {
    setFilterType(value);
    setParams({ type: value === "all" ? undefined : value, page: 1 });
  };

  // Handle filter status
  const handleFilterStatus = (value: string) => {
    setFilterStatus(value);
    setParams({ status: value === "all" ? undefined : value, page: 1 });
  };

  // Handle view promotion details
  const handleViewPromotion = (promotion: Promotion) => {
    setSelectedPromotion(promotion);
  };

  // Handle edit promotion
  const handleEditPromotion = (promotion: Promotion) => {
    setFormData({
      name: promotion.name,
      description: promotion.description || "",
      code: promotion.code,
      type: promotion.type,
      value: promotion.value,
      minOrderAmount: promotion.minOrderAmount?.toString() || "",
      maxDiscountAmount: promotion.maxDiscountAmount?.toString() || "",
      usageLimit: promotion.usageLimit?.toString() || "",
      userUsageLimit: promotion.userUsageLimit?.toString() || "",
      startDate: promotion.startDate.split("T")[0],
      endDate: promotion.endDate?.split("T")[0] || "",
      isActive: promotion.isActive,
    });
    setSelectedPromotion(promotion);
    setIsEditDialogOpen(true);
  };

  // Handle delete promotion
  const handleDeletePromotion = async (promotion: Promotion) => {
    if (confirm("Bạn có chắc chắn muốn xóa khuyến mãi này?")) {
      const result = await deletePromotion(promotion.id);
      if (result) {
        toast.success("Đã xóa khuyến mãi");
        refresh();
      }
    }
  };

  // Handle copy promotion code
  const handleCopyCode = (code: string) => {
    navigator.clipboard.writeText(code);
    toast.success("Đã sao chép mã khuyến mãi");
  };

  // Handle form submit
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const data = {
      ...formData,
      value: Number(formData.value),
      minOrderAmount: formData.minOrderAmount
        ? Number(formData.minOrderAmount)
        : undefined,
      maxDiscountAmount: formData.maxDiscountAmount
        ? Number(formData.maxDiscountAmount)
        : undefined,
      usageLimit: formData.usageLimit ? Number(formData.usageLimit) : undefined,
      userUsageLimit: formData.userUsageLimit
        ? Number(formData.userUsageLimit)
        : undefined,
      startDate: new Date(formData.startDate).toISOString(),
      endDate: formData.endDate
        ? new Date(formData.endDate).toISOString()
        : undefined,
    };

    let result;
    if (selectedPromotion && isEditDialogOpen) {
      result = await updatePromotion(selectedPromotion.id, data);
    } else {
      result = await createPromotion(data);
    }

    if (result) {
      toast.success(
        selectedPromotion ? "Đã cập nhật khuyến mãi" : "Đã tạo khuyến mãi mới"
      );
      setIsCreateDialogOpen(false);
      setIsEditDialogOpen(false);
      setSelectedPromotion(null);
      resetForm();
      refresh();
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      code: "",
      type: "PERCENTAGE",
      value: 0,
      minOrderAmount: "",
      maxDiscountAmount: "",
      usageLimit: "",
      userUsageLimit: "",
      startDate: "",
      endDate: "",
      isActive: true,
    });
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount);
  };

  // Format discount value
  const formatDiscountValue = (promotion: Promotion) => {
    switch (promotion.type) {
      case "PERCENTAGE":
        return `${promotion.value}%`;
      case "FIXED_AMOUNT":
        return formatCurrency(promotion.value);
      case "FREE_SHIPPING":
        return "Miễn phí ship";
      case "BUY_X_GET_Y":
        return `Mua ${promotion.value} tặng 1`;
      default:
        return promotion.value.toString();
    }
  };

  // Check if promotion is expired
  const isPromotionExpired = (promotion: Promotion) => {
    if (!promotion.endDate) return false;
    return new Date(promotion.endDate) < new Date();
  };

  // Check if promotion is active
  const isPromotionActive = (promotion: Promotion) => {
    const now = new Date();
    const startDate = new Date(promotion.startDate);
    const endDate = promotion.endDate ? new Date(promotion.endDate) : null;

    return (
      promotion.isActive && startDate <= now && (!endDate || endDate >= now)
    );
  };

  // Calculate promotion stats
  const getPromotionStats = () => {
    if (!promotions) return { total: 0, active: 0, expired: 0, totalUsage: 0 };

    const total = promotions.length;
    const active = promotions.filter((p) => isPromotionActive(p)).length;
    const expired = promotions.filter((p) => isPromotionExpired(p)).length;
    const totalUsage = promotions.reduce((sum, p) => sum + p.usageCount, 0);

    return { total, active, expired, totalUsage };
  };

  // Table configuration
  const promotionTableConfig = {
    columns: [
      {
        key: "promotion",
        title: "Khuyến mãi",
        render: (promotion: Promotion) => (
          <div className="space-y-1">
            <div className="font-medium">{promotion.name}</div>
            <div className="flex items-center gap-2">
              <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                {promotion.code}
              </code>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleCopyCode(promotion.code)}
                className="h-6 w-6 p-0"
              >
                <Copy className="h-3 w-3" />
              </Button>
            </div>
          </div>
        ),
      },
      {
        key: "type",
        title: "Loại",
        render: (promotion: Promotion) => (
          <Badge
            variant="secondary"
            className={promotionTypeColors[promotion.type]}
          >
            {promotionTypeLabels[promotion.type]}
          </Badge>
        ),
      },
      {
        key: "value",
        title: "Giá trị",
        render: (promotion: Promotion) => (
          <div className="font-medium">{formatDiscountValue(promotion)}</div>
        ),
      },
      {
        key: "usage",
        title: "Sử dụng",
        render: (promotion: Promotion) => (
          <div className="space-y-1">
            <div className="text-sm">
              {promotion.usageCount}
              {promotion.usageLimit && ` / ${promotion.usageLimit}`}
            </div>
            {promotion.usageLimit && (
              <div className="w-full bg-gray-200 rounded-full h-1">
                <div
                  className="bg-blue-600 h-1 rounded-full"
                  style={{
                    width: `${Math.min((promotion.usageCount / promotion.usageLimit) * 100, 100)}%`,
                  }}
                />
              </div>
            )}
          </div>
        ),
      },
      {
        key: "period",
        title: "Thời gian",
        render: (promotion: Promotion) => (
          <div className="space-y-1 text-sm">
            <div>
              Từ: {new Date(promotion.startDate).toLocaleDateString("vi-VN")}
            </div>
            {promotion.endDate && (
              <div>
                Đến: {new Date(promotion.endDate).toLocaleDateString("vi-VN")}
              </div>
            )}
          </div>
        ),
      },
      {
        key: "status",
        title: "Trạng thái",
        render: (promotion: Promotion) => {
          if (isPromotionExpired(promotion)) {
            return (
              <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                Hết hạn
              </Badge>
            );
          }
          if (isPromotionActive(promotion)) {
            return (
              <Badge
                variant="secondary"
                className="bg-green-100 text-green-800"
              >
                Đang hoạt động
              </Badge>
            );
          }
          if (!promotion.isActive) {
            return (
              <Badge variant="secondary" className="bg-red-100 text-red-800">
                Tạm dừng
              </Badge>
            );
          }
          return (
            <Badge
              variant="secondary"
              className="bg-yellow-100 text-yellow-800"
            >
              Chưa bắt đầu
            </Badge>
          );
        },
      },
    ],
    rowKey: "id",
    actions: {
      enabled: true,
      items: [
        {
          key: "view",
          label: "Xem chi tiết",
          icon: Eye,
          onClick: handleViewPromotion,
        },
        {
          key: "edit",
          label: "Chỉnh sửa",
          icon: Edit,
          onClick: handleEditPromotion,
        },
        {
          key: "delete",
          label: "Xóa",
          icon: Trash2,
          onClick: handleDeletePromotion,
          type: "danger" as const,
        },
      ],
    },
    selection: {
      enabled: true,
      type: "checkbox" as const,
    },
  };

  const stats = getPromotionStats();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-pink-100 rounded-lg flex items-center justify-center">
            <Tag className="h-5 w-5 text-pink-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">Quản lý Khuyến mãi</h1>
            <p className="text-muted-foreground">
              Quản lý mã giảm giá và các chương trình khuyến mãi
            </p>
          </div>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              Tạo khuyến mãi
            </Button>
          </DialogTrigger>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Tổng khuyến mãi
                </p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
              <Tag className="h-8 w-8 text-pink-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Đang hoạt động
                </p>
                <p className="text-2xl font-bold">{stats.active}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Đã hết hạn
                </p>
                <p className="text-2xl font-bold">{stats.expired}</p>
              </div>
              <Calendar className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Tổng lượt sử dụng
                </p>
                <p className="text-2xl font-bold">{stats.totalUsage}</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Tìm kiếm theo tên, mã khuyến mãi..."
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={filterType} onValueChange={handleFilterType}>
                <SelectTrigger className="w-[150px]">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Loại" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả</SelectItem>
                  <SelectItem value="PERCENTAGE">Giảm theo %</SelectItem>
                  <SelectItem value="FIXED_AMOUNT">Giảm cố định</SelectItem>
                  <SelectItem value="FREE_SHIPPING">Miễn phí ship</SelectItem>
                  <SelectItem value="BUY_X_GET_Y">Mua X tặng Y</SelectItem>
                </SelectContent>
              </Select>
              <Select value={filterStatus} onValueChange={handleFilterStatus}>
                <SelectTrigger className="w-[150px]">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Trạng thái" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả</SelectItem>
                  <SelectItem value="active">Đang hoạt động</SelectItem>
                  <SelectItem value="inactive">Tạm dừng</SelectItem>
                  <SelectItem value="expired">Hết hạn</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Promotions Table */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách Khuyến mãi</CardTitle>
        </CardHeader>
        <CardContent>
          <AdminDataTable
            data={promotions || []}
            config={promotionTableConfig}
            loading={loading}
            pagination={pagination}
            onPageChange={(page) => setParams({ page })}
            onPageSizeChange={(limit) => setParams({ limit, page: 1 })}
          />
        </CardContent>
      </Card>

      {/* Create/Edit Promotion Dialog */}
      <Dialog
        open={isCreateDialogOpen || isEditDialogOpen}
        onOpenChange={(open) => {
          if (!open) {
            setIsCreateDialogOpen(false);
            setIsEditDialogOpen(false);
            setSelectedPromotion(null);
            resetForm();
          }
        }}
      >
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {selectedPromotion
                ? "Chỉnh sửa khuyến mãi"
                : "Tạo khuyến mãi mới"}
            </DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Tên khuyến mãi *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) =>
                    setFormData({ ...formData, name: e.target.value })
                  }
                  placeholder="Nhập tên khuyến mãi"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="code">Mã khuyến mãi *</Label>
                <Input
                  id="code"
                  value={formData.code}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      code: e.target.value.toUpperCase(),
                    })
                  }
                  placeholder="Nhập mã khuyến mãi"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Mô tả</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) =>
                  setFormData({ ...formData, description: e.target.value })
                }
                placeholder="Nhập mô tả khuyến mãi"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="type">Loại khuyến mãi *</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) =>
                    setFormData({ ...formData, type: value as any })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PERCENTAGE">Giảm theo %</SelectItem>
                    <SelectItem value="FIXED_AMOUNT">Giảm cố định</SelectItem>
                    <SelectItem value="FREE_SHIPPING">Miễn phí ship</SelectItem>
                    <SelectItem value="BUY_X_GET_Y">Mua X tặng Y</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="value">
                  Giá trị *{formData.type === "PERCENTAGE" && " (%)"}
                  {formData.type === "FIXED_AMOUNT" && " (VND)"}
                  {formData.type === "BUY_X_GET_Y" && " (Số lượng)"}
                </Label>
                <Input
                  id="value"
                  type="number"
                  value={formData.value}
                  onChange={(e) =>
                    setFormData({ ...formData, value: Number(e.target.value) })
                  }
                  placeholder="Nhập giá trị"
                  min="0"
                  step={formData.type === "PERCENTAGE" ? "0.1" : "1000"}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="minOrderAmount">Đơn hàng tối thiểu (VND)</Label>
                <Input
                  id="minOrderAmount"
                  type="number"
                  value={formData.minOrderAmount}
                  onChange={(e) =>
                    setFormData({ ...formData, minOrderAmount: e.target.value })
                  }
                  placeholder="Không giới hạn"
                  min="0"
                  step="1000"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="maxDiscountAmount">Giảm tối đa (VND)</Label>
                <Input
                  id="maxDiscountAmount"
                  type="number"
                  value={formData.maxDiscountAmount}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      maxDiscountAmount: e.target.value,
                    })
                  }
                  placeholder="Không giới hạn"
                  min="0"
                  step="1000"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="usageLimit">Giới hạn sử dụng</Label>
                <Input
                  id="usageLimit"
                  type="number"
                  value={formData.usageLimit}
                  onChange={(e) =>
                    setFormData({ ...formData, usageLimit: e.target.value })
                  }
                  placeholder="Không giới hạn"
                  min="1"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="userUsageLimit">Giới hạn/người dùng</Label>
                <Input
                  id="userUsageLimit"
                  type="number"
                  value={formData.userUsageLimit}
                  onChange={(e) =>
                    setFormData({ ...formData, userUsageLimit: e.target.value })
                  }
                  placeholder="Không giới hạn"
                  min="1"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startDate">Ngày bắt đầu *</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={formData.startDate}
                  onChange={(e) =>
                    setFormData({ ...formData, startDate: e.target.value })
                  }
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="endDate">Ngày kết thúc</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={formData.endDate}
                  onChange={(e) =>
                    setFormData({ ...formData, endDate: e.target.value })
                  }
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, isActive: checked })
                }
              />
              <Label htmlFor="isActive">Kích hoạt khuyến mãi</Label>
            </div>

            <div className="flex justify-end gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsCreateDialogOpen(false);
                  setIsEditDialogOpen(false);
                  setSelectedPromotion(null);
                  resetForm();
                }}
              >
                Hủy
              </Button>
              <Button type="submit">
                {selectedPromotion ? "Cập nhật" : "Tạo mới"}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Promotion Detail Modal */}
      {selectedPromotion && !isEditDialogOpen && (
        <Dialog
          open={!!selectedPromotion}
          onOpenChange={() => setSelectedPromotion(null)}
        >
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Chi tiết Khuyến mãi</DialogTitle>
            </DialogHeader>
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-lg">
                      {selectedPromotion.name}
                    </h3>
                    <p className="text-muted-foreground">
                      {selectedPromotion.description}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <code className="text-sm bg-gray-100 px-3 py-1 rounded">
                      {selectedPromotion.code}
                    </code>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleCopyCode(selectedPromotion.code)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Loại:</span>
                    <Badge
                      className={promotionTypeColors[selectedPromotion.type]}
                    >
                      {promotionTypeLabels[selectedPromotion.type]}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Giá trị:</span>
                    <span className="font-semibold">
                      {formatDiscountValue(selectedPromotion)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Sử dụng:</span>
                    <span>
                      {selectedPromotion.usageCount}
                      {selectedPromotion.usageLimit &&
                        ` / ${selectedPromotion.usageLimit}`}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Trạng thái:</span>
                    <span>
                      {isPromotionExpired(selectedPromotion) && "Hết hạn"}
                      {isPromotionActive(selectedPromotion) && "Đang hoạt động"}
                      {!selectedPromotion.isActive && "Tạm dừng"}
                      {!isPromotionExpired(selectedPromotion) &&
                        !isPromotionActive(selectedPromotion) &&
                        selectedPromotion.isActive &&
                        "Chưa bắt đầu"}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
