"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Star,
  Search,
  Filter,
  Eye,
  Check,
  X,
  MessageSquare,
  User,
  Package,
  Calendar,
  Clock,
  Image as ImageIcon,
} from "lucide-react";
import { toast } from "sonner";
import { AdminDataTable } from "@/lib/admin/components/AdminDataTable";
import { useAdminData, useAdminCrud } from "@/lib/admin/hooks";
import { formatDistanceToNow } from "date-fns";
import { vi } from "date-fns/locale";

interface Review {
  id: string;
  rating: number;
  comment?: string;
  images: string[];
  status: "PENDING" | "APPROVED" | "REJECTED";
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
  };
  product: {
    id: string;
    name: string;
    images: string[];
    slug: string;
  };
}

const reviewStatusLabels = {
  PENDING: "Chờ duyệt",
  APPROVED: "Đã duyệt",
  REJECTED: "Đã từ chối",
};

const reviewStatusColors = {
  PENDING: "bg-yellow-100 text-yellow-800",
  APPROVED: "bg-green-100 text-green-800",
  REJECTED: "bg-red-100 text-red-800",
};

export default function AdminReviewsPage() {
  const [selectedReview, setSelectedReview] = useState<Review | null>(null);
  const [filterStatus, setFilterStatus] = useState<string>("");
  const [filterRating, setFilterRating] = useState<string>("");
  const [searchQuery, setSearchQuery] = useState<string>("");

  // Data fetching
  const {
    data: reviews,
    loading,
    pagination,
    refresh,
    setParams,
  } = useAdminData<Review>({
    endpoint: "/api/admin/reviews",
    initialParams: { page: 1, limit: 20 },
  });

  // CRUD operations
  const { update: updateReview } = useAdminCrud("/api/admin/reviews");

  // Handle search
  const handleSearch = (value: string) => {
    setSearchQuery(value);
    setParams({ search: value, page: 1 });
  };

  // Handle filter status
  const handleFilterStatus = (value: string) => {
    setFilterStatus(value);
    setParams({ status: value === "all" ? undefined : value, page: 1 });
  };

  // Handle filter rating
  const handleFilterRating = (value: string) => {
    setFilterRating(value);
    setParams({ rating: value === "all" ? undefined : value, page: 1 });
  };

  // Handle view review details
  const handleViewReview = (review: Review) => {
    setSelectedReview(review);
  };

  // Handle approve review
  const handleApproveReview = async (review: Review) => {
    const result = await updateReview(review.id, { status: "APPROVED" });
    if (result) {
      toast.success("Đã duyệt đánh giá");
      refresh();
    }
  };

  // Handle reject review
  const handleRejectReview = async (review: Review) => {
    const result = await updateReview(review.id, { status: "REJECTED" });
    if (result) {
      toast.success("Đã từ chối đánh giá");
      refresh();
    }
  };

  // Render star rating
  const renderStars = (rating: number, size: "sm" | "md" | "lg" = "sm") => {
    const sizeClasses = {
      sm: "h-3 w-3",
      md: "h-4 w-4",
      lg: "h-5 w-5",
    };

    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${sizeClasses[size]} ${
              star <= rating
                ? "text-yellow-400 fill-yellow-400"
                : "text-gray-300"
            }`}
          />
        ))}
      </div>
    );
  };

  // Calculate review stats
  const getReviewStats = () => {
    if (!reviews)
      return { total: 0, pending: 0, approved: 0, rejected: 0, avgRating: 0 };

    const total = reviews.length;
    const pending = reviews.filter((r) => r.status === "PENDING").length;
    const approved = reviews.filter((r) => r.status === "APPROVED").length;
    const rejected = reviews.filter((r) => r.status === "REJECTED").length;
    const avgRating =
      reviews.length > 0
        ? reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length
        : 0;

    return { total, pending, approved, rejected, avgRating };
  };

  // Table configuration
  const reviewTableConfig = {
    columns: [
      {
        key: "product",
        title: "Sản phẩm",
        render: (review: Review) => (
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gray-100 rounded-lg overflow-hidden">
              {review.product.images[0] ? (
                <img
                  src={review.product.images[0]}
                  alt={review.product.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <Package className="h-6 w-6 text-gray-400" />
                </div>
              )}
            </div>
            <div>
              <div className="font-medium line-clamp-1">
                {review.product.name}
              </div>
              <div className="text-sm text-muted-foreground">
                #{review.product.id.slice(-8)}
              </div>
            </div>
          </div>
        ),
      },
      {
        key: "customer",
        title: "Khách hàng",
        render: (review: Review) => (
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center">
              {review.user.avatar ? (
                <img
                  src={review.user.avatar}
                  alt={review.user.name}
                  className="w-8 h-8 rounded-full object-cover"
                />
              ) : (
                <User className="h-4 w-4 text-pink-600" />
              )}
            </div>
            <div>
              <div className="font-medium">{review.user.name}</div>
              <div className="text-sm text-muted-foreground">
                {review.user.email}
              </div>
            </div>
          </div>
        ),
      },
      {
        key: "rating",
        title: "Đánh giá",
        render: (review: Review) => (
          <div className="space-y-1">
            {renderStars(review.rating, "md")}
            <div className="text-sm text-muted-foreground">
              {review.rating}/5 sao
            </div>
          </div>
        ),
      },
      {
        key: "comment",
        title: "Bình luận",
        render: (review: Review) => (
          <div className="max-w-xs">
            {review.comment ? (
              <div>
                <p className="text-sm line-clamp-2">{review.comment}</p>
                {review.images.length > 0 && (
                  <div className="flex items-center mt-1 text-xs text-muted-foreground">
                    <ImageIcon className="h-3 w-3 mr-1" />
                    {review.images.length} hình ảnh
                  </div>
                )}
              </div>
            ) : (
              <span className="text-sm text-muted-foreground">
                Không có bình luận
              </span>
            )}
          </div>
        ),
      },
      {
        key: "status",
        title: "Trạng thái",
        render: (review: Review) => (
          <Badge
            variant="secondary"
            className={reviewStatusColors[review.status]}
          >
            {reviewStatusLabels[review.status]}
          </Badge>
        ),
      },
      {
        key: "createdAt",
        title: "Ngày tạo",
        sortable: true,
        render: (review: Review) => (
          <div className="text-sm">
            {formatDistanceToNow(new Date(review.createdAt), {
              addSuffix: true,
              locale: vi,
            })}
          </div>
        ),
      },
    ],
    rowKey: "id",
    actions: {
      enabled: true,
      items: [
        {
          key: "view",
          label: "Xem chi tiết",
          icon: Eye,
          onClick: handleViewReview,
        },
        {
          key: "approve",
          label: "Duyệt",
          icon: Check,
          onClick: handleApproveReview,
          type: "primary" as const,
          condition: (review: Review) => review.status === "PENDING",
        },
        {
          key: "reject",
          label: "Từ chối",
          icon: X,
          onClick: handleRejectReview,
          type: "danger" as const,
          condition: (review: Review) => review.status === "PENDING",
        },
      ],
    },
    selection: {
      enabled: true,
      type: "checkbox" as const,
    },
  };

  const stats = getReviewStats();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-pink-100 rounded-lg flex items-center justify-center">
            <MessageSquare className="h-5 w-5 text-pink-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">Quản lý Đánh giá</h1>
            <p className="text-muted-foreground">
              Quản lý và kiểm duyệt đánh giá sản phẩm từ khách hàng
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Tổng đánh giá
                </p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
              <MessageSquare className="h-8 w-8 text-pink-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Chờ duyệt
                </p>
                <p className="text-2xl font-bold">{stats.pending}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Đã duyệt
                </p>
                <p className="text-2xl font-bold">{stats.approved}</p>
              </div>
              <Check className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Đã từ chối
                </p>
                <p className="text-2xl font-bold">{stats.rejected}</p>
              </div>
              <X className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Đánh giá TB
                </p>
                <div className="flex items-center gap-2">
                  <p className="text-2xl font-bold">
                    {stats.avgRating.toFixed(1)}
                  </p>
                  {renderStars(Math.round(stats.avgRating), "sm")}
                </div>
              </div>
              <Star className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Tìm kiếm theo sản phẩm, khách hàng, bình luận..."
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={filterStatus} onValueChange={handleFilterStatus}>
                <SelectTrigger className="w-[150px]">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Trạng thái" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả</SelectItem>
                  <SelectItem value="PENDING">Chờ duyệt</SelectItem>
                  <SelectItem value="APPROVED">Đã duyệt</SelectItem>
                  <SelectItem value="REJECTED">Đã từ chối</SelectItem>
                </SelectContent>
              </Select>
              <Select value={filterRating} onValueChange={handleFilterRating}>
                <SelectTrigger className="w-[150px]">
                  <Star className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Số sao" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả</SelectItem>
                  <SelectItem value="5">5 sao</SelectItem>
                  <SelectItem value="4">4 sao</SelectItem>
                  <SelectItem value="3">3 sao</SelectItem>
                  <SelectItem value="2">2 sao</SelectItem>
                  <SelectItem value="1">1 sao</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Reviews Table */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách Đánh giá</CardTitle>
        </CardHeader>
        <CardContent>
          <AdminDataTable
            data={reviews || []}
            config={reviewTableConfig}
            loading={loading}
            pagination={pagination}
            onPageChange={(page) => setParams({ page })}
            onPageSizeChange={(limit) => setParams({ limit, page: 1 })}
          />
        </CardContent>
      </Card>

      {/* Review Detail Modal */}
      {selectedReview && (
        <Dialog
          open={!!selectedReview}
          onOpenChange={() => setSelectedReview(null)}
        >
          <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Chi tiết Đánh giá</DialogTitle>
            </DialogHeader>
            <div className="space-y-6">
              {/* Review Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">
                      Thông tin sản phẩm
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden">
                        {selectedReview.product.images[0] ? (
                          <img
                            src={selectedReview.product.images[0]}
                            alt={selectedReview.product.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <Package className="h-8 w-8 text-gray-400" />
                          </div>
                        )}
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg">
                          {selectedReview.product.name}
                        </h3>
                        <p className="text-muted-foreground">
                          #{selectedReview.product.id.slice(-8)}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">
                      Thông tin khách hàng
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center">
                        {selectedReview.user.avatar ? (
                          <img
                            src={selectedReview.user.avatar}
                            alt={selectedReview.user.name}
                            className="w-16 h-16 rounded-full object-cover"
                          />
                        ) : (
                          <User className="h-8 w-8 text-pink-600" />
                        )}
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg">
                          {selectedReview.user.name}
                        </h3>
                        <p className="text-muted-foreground">
                          {selectedReview.user.email}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Review Content */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Nội dung đánh giá</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {renderStars(selectedReview.rating, "lg")}
                      <span className="text-lg font-semibold">
                        {selectedReview.rating}/5 sao
                      </span>
                    </div>
                    <Badge
                      variant="secondary"
                      className={reviewStatusColors[selectedReview.status]}
                    >
                      {reviewStatusLabels[selectedReview.status]}
                    </Badge>
                  </div>

                  {selectedReview.comment && (
                    <div>
                      <h4 className="font-medium mb-2">Bình luận:</h4>
                      <p className="text-sm bg-gray-50 p-3 rounded-lg">
                        {selectedReview.comment}
                      </p>
                    </div>
                  )}

                  {selectedReview.images.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-2">Hình ảnh đính kèm:</h4>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                        {selectedReview.images.map((image, index) => (
                          <div
                            key={index}
                            className="aspect-square bg-gray-100 rounded-lg overflow-hidden"
                          >
                            <img
                              src={image}
                              alt={`Review image ${index + 1}`}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex items-center text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4 mr-2" />
                    Đánh giá vào{" "}
                    {formatDistanceToNow(new Date(selectedReview.createdAt), {
                      addSuffix: true,
                      locale: vi,
                    })}
                  </div>

                  {selectedReview.status === "PENDING" && (
                    <div className="flex gap-3 pt-4">
                      <Button
                        onClick={() => handleApproveReview(selectedReview)}
                        className="flex-1"
                      >
                        <Check className="h-4 w-4 mr-2" />
                        Duyệt đánh giá
                      </Button>
                      <Button
                        variant="destructive"
                        onClick={() => handleRejectReview(selectedReview)}
                        className="flex-1"
                      >
                        <X className="h-4 w-4 mr-2" />
                        Từ chối
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
