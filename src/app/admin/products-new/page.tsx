"use client";

import React, { useState } from "react";
import Link from "next/link";
import { Plus, Eye, Edit, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  AdminDataTable,
  AdminErrorState,
  AdminEmptyState,
} from "@/lib/admin/components";
import { useAdminData, useAdminCrud } from "@/lib/admin/hooks";
import { TableConfig, TableColumn, BulkAction } from "@/lib/admin/types";
import { formatCurrency } from "@/lib/utils";

interface Product {
  id: string;
  name: string;
  price: number;
  salePrice?: number;
  stock: number;
  status: "ACTIVE" | "INACTIVE" | "OUT_OF_STOCK";
  featured: boolean;
  category: {
    id: string;
    name: string;
  };
  images: string[];
  createdAt: string;
}

export default function ProductsNewPage() {
  const [selectedProducts, setSelectedProducts] = useState<Product[]>([]);

  // Use admin data hook for fetching products
  const {
    data: products,
    loading,
    error,
    pagination,
    refresh,
    setParams,
    params,
  } = useAdminData<Product>({
    endpoint: "/api/admin/products",
    initialParams: {
      page: 1,
      limit: 20,
    },
  });

  // Use admin CRUD hook for operations
  const {
    remove,
    bulkDelete,
    loading: crudLoading,
  } = useAdminCrud<Product>("/api/admin/products");

  // Table columns configuration
  const columns: TableColumn<Product>[] = [
    {
      key: "image",
      title: "Hình ảnh",
      width: 80,
      render: (record) => (
        <div className="w-12 h-12 bg-muted rounded-md overflow-hidden">
          {record.images[0] ? (
            <img
              src={record.images[0]}
              alt={record.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-muted-foreground">
              No Image
            </div>
          )}
        </div>
      ),
    },
    {
      key: "name",
      title: "Tên sản phẩm",
      dataIndex: "name",
      sortable: true,
      searchable: true,
    },
    {
      key: "category",
      title: "Danh mục",
      render: (record) => record.category.name,
    },
    {
      key: "price",
      title: "Giá",
      align: "right",
      render: (record) => (
        <div className="text-right">
          {record.salePrice ? (
            <>
              <div className="line-through text-muted-foreground text-sm">
                {formatCurrency(record.price)}
              </div>
              <div className="text-red-600 font-medium">
                {formatCurrency(record.salePrice)}
              </div>
            </>
          ) : (
            <div className="font-medium">{formatCurrency(record.price)}</div>
          )}
        </div>
      ),
    },
    {
      key: "stock",
      title: "Tồn kho",
      dataIndex: "stock",
      align: "center",
      render: (record) => (
        <Badge
          variant={
            record.stock > 10
              ? "default"
              : record.stock > 0
                ? "secondary"
                : "destructive"
          }
        >
          {record.stock}
        </Badge>
      ),
    },
    {
      key: "status",
      title: "Trạng thái",
      dataIndex: "status",
      render: (record) => {
        const variants = {
          ACTIVE: "default",
          INACTIVE: "secondary",
          OUT_OF_STOCK: "destructive",
        } as const;

        const labels = {
          ACTIVE: "Hoạt động",
          INACTIVE: "Không hoạt động",
          OUT_OF_STOCK: "Hết hàng",
        };

        return (
          <Badge variant={variants[record.status as keyof typeof variants]}>
            {labels[record.status as keyof typeof labels]}
          </Badge>
        );
      },
    },
    {
      key: "featured",
      title: "Nổi bật",
      dataIndex: "featured",
      align: "center",
      render: (record) => (
        <Badge variant={record.featured ? "default" : "outline"}>
          {record.featured ? "Có" : "Không"}
        </Badge>
      ),
    },
  ];

  // Table actions
  const actions = [
    {
      key: "view",
      label: "Xem",
      icon: <Eye className="h-4 w-4 mr-2" />,
      onClick: (record: Product) => {
        window.open(`/admin/products/${record.id}`, "_blank");
      },
    },
    {
      key: "edit",
      label: "Sửa",
      icon: <Edit className="h-4 w-4 mr-2" />,
      onClick: (record: Product) => {
        window.location.href = `/admin/products/${record.id}/edit`;
      },
    },
    {
      key: "delete",
      label: "Xóa",
      icon: <Trash2 className="h-4 w-4 mr-2" />,
      type: "danger" as const,
      onClick: async (record: Product) => {
        if (confirm(`Bạn có chắc chắn muốn xóa sản phẩm "${record.name}"?`)) {
          const success = await remove(record.id);
          if (success) {
            refresh();
          }
        }
      },
    },
  ];

  // Bulk actions
  const bulkActions: BulkAction<Product>[] = [
    {
      key: "delete",
      label: "Xóa đã chọn",
      icon: <Trash2 className="h-4 w-4 mr-2" />,
      type: "danger",
      onClick: async (selectedRows: Product[]) => {
        if (
          confirm(
            `Bạn có chắc chắn muốn xóa ${selectedRows.length} sản phẩm đã chọn?`
          )
        ) {
          const ids = selectedRows.map((row) => row.id);
          const success = await bulkDelete(ids);
          if (success) {
            setSelectedProducts([]);
            refresh();
          }
        }
      },
    },
  ];

  // Table configuration
  const tableConfig: TableConfig<Product> = {
    columns,
    rowKey: "id",
    pagination: {
      enabled: true,
      pageSize: params.limit,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: true,
    },
    selection: {
      enabled: true,
      type: "checkbox",
      onSelectionChange: setSelectedProducts,
    },
    actions: {
      enabled: true,
      items: actions,
    },
    search: {
      enabled: true,
      placeholder: "Tìm kiếm sản phẩm...",
      fields: ["name", "description"],
    },
    sorting: {
      enabled: true,
      defaultSort: {
        field: "createdAt",
        order: "desc",
      },
    },
    loading: loading || crudLoading,
    emptyText: "Chưa có sản phẩm nào",
  };

  if (error) {
    return (
      <AdminErrorState
        title="Lỗi tải dữ liệu"
        description="Không thể tải danh sách sản phẩm"
        error={error}
        onRetry={refresh}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Quản lý sản phẩm (Demo)</h1>
          <p className="text-muted-foreground">
            Quản lý sản phẩm sử dụng thư viện admin đã chuẩn hóa
          </p>
        </div>

        <Link href="/admin/products/create">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Thêm sản phẩm
          </Button>
        </Link>
      </div>

      {/* Data Table */}
      <AdminDataTable
        config={tableConfig}
        dataSource={products}
        loading={loading}
        onRefresh={refresh}
        onSearch={(search) => setParams({ search })}
        onPageChange={(page) => setParams({ page })}
        onPageSizeChange={(pageSize) => setParams({ limit: pageSize, page: 1 })}
        onSelectionChange={setSelectedProducts}
        bulkActions={bulkActions}
        searchValue={params.search || ""}
        selectedRows={selectedProducts}
        pagination={
          pagination
            ? {
                current: pagination.page,
                pageSize: pagination.limit,
                total: pagination.total,
              }
            : undefined
        }
      />

      {/* Empty State */}
      {!loading && products.length === 0 && !params.search && (
        <AdminEmptyState
          title="Chưa có sản phẩm"
          description="Bắt đầu bằng cách tạo sản phẩm đầu tiên của bạn"
          action={{
            label: "Thêm sản phẩm",
            onClick: () => (window.location.href = "/admin/products/create"),
          }}
          icon={<Plus className="h-12 w-12" />}
        />
      )}
    </div>
  );
}
